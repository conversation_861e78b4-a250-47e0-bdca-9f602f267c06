{"name": "bargainhawk-chrome-extension", "version": "1.0.0", "description": "BargainHawk Price Tracker Chrome Extension - Track prices from Costco, Walmart, Wayfair and other Canadian retailers", "main": "background.js", "scripts": {"build": "npm run build:icons && npm run build:extension", "build:icons": "node create-icons.js", "build:extension": "npm run copy:files", "copy:files": "npm run clean && mkdir -p dist && cp -r *.js *.html *.css *.json icons dist/", "clean": "rm -rf dist", "dev": "npm run build && npm run watch", "watch": "nodemon --watch . --ext js,html,css,json --ignore dist --ignore node_modules --exec 'npm run build'", "test": "npm run test:lint && npm run test:extension", "test:lint": "eslint *.js", "test:extension": "node test-extension.js", "package": "npm run build && npm run zip", "zip": "cd dist && zip -r ../bargainhawk-extension-v1.0.0.zip .", "install-dev": "npm run build && node install-dev-extension.js", "validate": "node validate-extension.js"}, "keywords": ["chrome-extension", "price-tracker", "bargainhawk", "costco", "walmart", "wayfair", "canada", "shopping", "deals"], "author": "BargainHawk", "license": "MIT", "devDependencies": {"eslint": "^8.57.0", "nodemon": "^3.0.2", "chrome-webstore-upload": "^3.0.0"}, "dependencies": {}, "repository": {"type": "git", "url": "https://github.com/yourusername/bargainhawk-extension.git"}, "bugs": {"url": "https://github.com/yourusername/bargainhawk-extension/issues"}, "homepage": "https://bargainhawk.ca", "engines": {"node": ">=16.0.0"}}