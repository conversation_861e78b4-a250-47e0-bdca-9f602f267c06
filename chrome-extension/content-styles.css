/* BargainHawk Chrome Extension - Content Script Styles */

/* Floating Action Button */
#bargainhawk-float-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 10000;
  cursor: pointer;
  animation: fadeInUp 0.3s ease-out;
}

.bh-float-button {
  display: flex;
  align-items: center;
  background: #3b82f6;
  color: white;
  padding: 12px 16px;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  font-weight: 500;
}

.bh-float-button:hover {
  background: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5);
}

.bh-float-button img {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

/* Price Selection Overlay */
#bargainhawk-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.2s ease-out;
}

.bh-overlay-content {
  background: white;
  padding: 24px;
  border-radius: 12px;
  text-align: center;
  max-width: 400px;
  margin: 20px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.bh-overlay-content h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1e293b;
}

.bh-overlay-content p {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 20px;
  line-height: 1.5;
}

#bh-cancel-selection {
  background: #6b7280;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

#bh-cancel-selection:hover {
  background: #4b5563;
}

/* Price Element Highlighting */
.bh-price-highlight {
  outline: 2px solid #3b82f6 !important;
  background: rgba(59, 130, 246, 0.1) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.bh-price-highlight:hover {
  outline: 3px solid #2563eb !important;
  background: rgba(37, 99, 235, 0.15) !important;
}

/* Success Message */
#bargainhawk-success {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10002;
  animation: slideInRight 0.3s ease-out;
}

.bh-success-message {
  background: #10b981;
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  display: flex;
  align-items: center;
}

/* Product Detection Indicator */
.bh-product-detected {
  position: fixed;
  top: 20px;
  left: 20px;
  background: #059669;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 12px;
  font-weight: 500;
  z-index: 10000;
  animation: fadeInDown 0.3s ease-out;
}

/* Price Tracking Badge */
.bh-tracking-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #3b82f6;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  z-index: 1000;
  animation: pulse 2s infinite;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #bargainhawk-float-btn {
    bottom: 80px; /* Avoid mobile browser UI */
  }
  
  .bh-float-button {
    padding: 10px 14px;
    font-size: 13px;
  }
  
  .bh-float-button img {
    width: 18px;
    height: 18px;
  }
  
  .bh-overlay-content {
    margin: 10px;
    padding: 20px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .bh-overlay-content {
    background: #1e293b;
    color: #f1f5f9;
  }
  
  .bh-overlay-content h3 {
    color: #f1f5f9;
  }
  
  .bh-overlay-content p {
    color: #94a3b8;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bh-price-highlight {
    outline: 3px solid #000 !important;
    background: #ffff00 !important;
  }
  
  .bh-float-button {
    border: 2px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
