<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BargainHawk Price Tracker</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div id="app">
    <!-- Loading State -->
    <div id="loading" class="screen">
      <div class="loading-spinner"></div>
      <p>Loading BargainHawk...</p>
    </div>

    <!-- Login Screen -->
    <div id="login-screen" class="screen hidden">
      <div class="header">
        <img src="icons/icon48.png" alt="BargainHawk" class="logo">
        <h1>BargainHawk</h1>
        <p>Price Tracker Extension</p>
      </div>
      
      <div class="login-form">
        <h2>Sign In</h2>
        <form id="login-form">
          <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" required>
          </div>
          
          <div class="form-group">
            <label for="password">Password</label>
            <input type="password" id="password" name="password" required>
          </div>
          
          <button type="submit" class="btn btn-primary" id="login-btn">
            Sign In
          </button>
        </form>
        
        <div class="login-links">
          <a href="https://bargainhawk.ca/signup" target="_blank">Create Account</a>
          <a href="https://bargainhawk.ca/forgot-password" target="_blank">Forgot Password?</a>
        </div>
      </div>
    </div>

    <!-- Main Screen - Product Detection -->
    <div id="main-screen" class="screen hidden">
      <div class="header">
        <img src="icons/icon32.png" alt="BargainHawk" class="logo-small">
        <div class="user-info">
          <span id="user-name">Welcome!</span>
          <button id="logout-btn" class="btn-link">Logout</button>
        </div>
      </div>

      <!-- Retailer Detection -->
      <div id="retailer-info" class="retailer-section">
        <div class="retailer-detected">
          <span class="retailer-icon">🏪</span>
          <div>
            <strong id="retailer-name">Detecting retailer...</strong>
            <p id="page-status">Checking page...</p>
          </div>
        </div>
      </div>

      <!-- Product Preview -->
      <div id="product-preview" class="product-section hidden">
        <h3>Product Found</h3>
        <div class="product-card">
          <img id="product-image" src="" alt="Product" class="product-img">
          <div class="product-details">
            <h4 id="product-name">Product Name</h4>
            <p class="product-price">$<span id="product-price">0.00</span></p>
            <p class="product-retailer" id="product-retailer">Retailer</p>
          </div>
        </div>
        
        <div class="price-target">
          <label for="target-price">Target Price (optional)</label>
          <div class="price-input">
            <span>$</span>
            <input type="number" id="target-price" placeholder="Enter target price" step="0.01" min="0">
          </div>
        </div>
        
        <button id="add-product-btn" class="btn btn-primary">
          Add to BargainHawk
        </button>
      </div>

      <!-- Price Selection Mode -->
      <div id="price-selection" class="selection-section hidden">
        <h3>Select Price Element</h3>
        <p>Click on the price on the webpage to teach BargainHawk where to find it.</p>
        
        <div class="selection-actions">
          <button id="start-selection-btn" class="btn btn-secondary">
            Start Price Selection
          </button>
          <button id="auto-detect-btn" class="btn btn-primary">
            Auto-Detect Product
          </button>
        </div>
      </div>

      <!-- Success/Error Messages -->
      <div id="message-area" class="message-area hidden">
        <div id="success-message" class="message success hidden">
          <span class="message-icon">✅</span>
          <span class="message-text">Product added successfully!</span>
        </div>
        
        <div id="error-message" class="message error hidden">
          <span class="message-icon">❌</span>
          <span class="message-text">Error adding product</span>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions">
        <button id="view-dashboard-btn" class="btn btn-outline">
          View Dashboard
        </button>
        <button id="settings-btn" class="btn btn-outline">
          Settings
        </button>
      </div>
    </div>

    <!-- Settings Screen -->
    <div id="settings-screen" class="screen hidden">
      <div class="header">
        <button id="back-btn" class="btn-back">←</button>
        <h2>Settings</h2>
      </div>
      
      <div class="settings-content">
        <div class="setting-group">
          <h3>Supported Retailers</h3>
          <div class="retailer-toggles">
            <label class="toggle">
              <input type="checkbox" id="costco-enabled" checked>
              <span class="toggle-slider"></span>
              Costco Canada
            </label>
            
            <label class="toggle">
              <input type="checkbox" id="walmart-enabled" checked>
              <span class="toggle-slider"></span>
              Walmart Canada
            </label>
            
            <label class="toggle">
              <input type="checkbox" id="wayfair-enabled" checked>
              <span class="toggle-slider"></span>
              Wayfair Canada
            </label>
            
            <label class="toggle">
              <input type="checkbox" id="bestbuy-enabled" checked>
              <span class="toggle-slider"></span>
              Best Buy Canada
            </label>
          </div>
        </div>
        
        <div class="setting-group">
          <h3>Notifications</h3>
          <label class="toggle">
            <input type="checkbox" id="price-drop-notifications" checked>
            <span class="toggle-slider"></span>
            Price Drop Alerts
          </label>
          
          <label class="toggle">
            <input type="checkbox" id="success-notifications" checked>
            <span class="toggle-slider"></span>
            Success Messages
          </label>
        </div>
        
        <div class="setting-group">
          <h3>Extension Behavior</h3>
          <label class="toggle">
            <input type="checkbox" id="auto-detect-products" checked>
            <span class="toggle-slider"></span>
            Auto-detect Products
          </label>
          
          <label class="toggle">
            <input type="checkbox" id="show-floating-button" checked>
            <span class="toggle-slider"></span>
            Show Floating Button
          </label>
        </div>
        
        <div class="setting-group">
          <h3>Account</h3>
          <p>Logged in as: <span id="settings-user-email"><EMAIL></span></p>
          <button id="clear-data-btn" class="btn btn-danger">
            Clear Extension Data
          </button>
        </div>
      </div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
