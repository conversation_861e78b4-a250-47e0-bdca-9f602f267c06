// BargainHawk Chrome Extension - Background Script (Service Worker)

class BargainHawkBackground {
  constructor() {
    this.init();
  }
  
  init() {
    // Handle extension installation
    chrome.runtime.onInstalled.addListener(this.handleInstall.bind(this));
    
    // Handle messages from content scripts and popup
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));
    
    // Handle tab updates to inject content scripts
    chrome.tabs.onUpdated.addListener(this.handleTabUpdate.bind(this));
    
    // Handle extension icon click
    chrome.action.onClicked.addListener(this.handleIconClick.bind(this));
    
    // Set up periodic price checking (if needed)
    this.setupPeriodicTasks();
  }
  
  handleInstall(details) {
    if (details.reason === 'install') {
      // First time installation
      this.showWelcomeMessage();
      
      // Set default settings
      chrome.storage.local.set({
        'costco-enabled': true,
        'walmart-enabled': true,
        'wayfair-enabled': true,
        'bestbuy-enabled': true,
        'price-drop-notifications': true,
        'success-notifications': true,
        'auto-detect-products': true,
        'show-floating-button': true
      });
      
      // Open welcome page
      chrome.tabs.create({
        url: 'https://bargainhawk.ca/extension-welcome'
      });
    } else if (details.reason === 'update') {
      // Extension updated
      console.log('BargainHawk extension updated to version', chrome.runtime.getManifest().version);
    }
  }
  
  handleMessage(request, sender, sendResponse) {
    switch (request.action) {
      case 'openPopup':
        this.openPopup(sender.tab);
        break;
        
      case 'checkAuth':
        this.checkAuthStatus().then(sendResponse);
        return true; // Keep message channel open for async response
        
      case 'addProduct':
        this.addProductToAPI(request.data).then(sendResponse);
        return true;
        
      case 'getSettings':
        this.getExtensionSettings().then(sendResponse);
        return true;
        
      case 'updateSettings':
        this.updateExtensionSettings(request.settings).then(sendResponse);
        return true;
        
      case 'showNotification':
        this.showNotification(request.title, request.message, request.type);
        break;
        
      default:
        console.log('Unknown message action:', request.action);
    }
  }
  
  handleTabUpdate(tabId, changeInfo, tab) {
    // Only process when page is completely loaded
    if (changeInfo.status !== 'complete') return;
    
    // Check if tab URL matches supported retailers
    if (this.isSupportedRetailer(tab.url)) {
      // Inject content script if not already injected
      this.injectContentScript(tabId);
    }
  }
  
  handleIconClick(tab) {
    // This is handled by the popup, but we can add additional logic here if needed
    console.log('Extension icon clicked on tab:', tab.url);
  }
  
  isSupportedRetailer(url) {
    if (!url) return false;
    
    const supportedDomains = [
      'costco.ca',
      'www.costco.ca',
      'walmart.ca',
      'www.walmart.ca',
      'wayfair.ca',
      'www.wayfair.ca',
      'bestbuy.ca',
      'www.bestbuy.ca',
      'canadiantire.ca',
      'www.canadiantire.ca',
      'amazon.ca',
      'www.amazon.ca'
    ];
    
    return supportedDomains.some(domain => url.includes(domain));
  }
  
  async injectContentScript(tabId) {
    try {
      // Check if content script is already injected
      const results = await chrome.scripting.executeScript({
        target: { tabId: tabId },
        func: () => window.bargainHawkInjected === true
      });
      
      if (!results[0].result) {
        // Inject content script
        await chrome.scripting.executeScript({
          target: { tabId: tabId },
          files: ['content-script.js']
        });
        
        // Inject CSS
        await chrome.scripting.insertCSS({
          target: { tabId: tabId },
          files: ['content-styles.css']
        });
        
        console.log('Content script injected into tab:', tabId);
      }
    } catch (error) {
      console.error('Error injecting content script:', error);
    }
  }
  
  async checkAuthStatus() {
    try {
      const result = await chrome.storage.local.get(['authToken', 'user']);
      return {
        isAuthenticated: !!(result.authToken && result.user),
        user: result.user || null
      };
    } catch (error) {
      console.error('Error checking auth status:', error);
      return { isAuthenticated: false, user: null };
    }
  }
  
  async addProductToAPI(productData) {
    try {
      const authResult = await chrome.storage.local.get(['authToken']);
      if (!authResult.authToken) {
        throw new Error('Not authenticated');
      }
      
      const response = await fetch('https://api.bargainhawk.ca/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authResult.authToken}`
        },
        body: JSON.stringify({
          ...productData,
          addedVia: 'chrome-extension',
          timestamp: Date.now()
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        // Show success notification
        this.showNotification(
          'Product Added!',
          `${productData.name} is now being tracked`,
          'success'
        );
        
        return { success: true, data: data };
      } else {
        throw new Error(data.message || 'Failed to add product');
      }
    } catch (error) {
      console.error('Error adding product:', error);
      
      this.showNotification(
        'Error',
        'Failed to add product: ' + error.message,
        'error'
      );
      
      return { success: false, error: error.message };
    }
  }
  
  async getExtensionSettings() {
    const defaultSettings = {
      'costco-enabled': true,
      'walmart-enabled': true,
      'wayfair-enabled': true,
      'bestbuy-enabled': true,
      'price-drop-notifications': true,
      'success-notifications': true,
      'auto-detect-products': true,
      'show-floating-button': true
    };
    
    const stored = await chrome.storage.local.get(Object.keys(defaultSettings));
    
    // Merge with defaults
    return { ...defaultSettings, ...stored };
  }
  
  async updateExtensionSettings(settings) {
    try {
      await chrome.storage.local.set(settings);
      return { success: true };
    } catch (error) {
      console.error('Error updating settings:', error);
      return { success: false, error: error.message };
    }
  }
  
  showNotification(title, message, type = 'info') {
    // Check if notifications are enabled
    chrome.storage.local.get(['success-notifications', 'price-drop-notifications'], (result) => {
      const showSuccess = result['success-notifications'] !== false;
      const showPriceDrop = result['price-drop-notifications'] !== false;
      
      if ((type === 'success' && !showSuccess) || (type === 'price-drop' && !showPriceDrop)) {
        return;
      }
      
      // Create notification
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: title,
        message: message,
        priority: type === 'error' ? 2 : 1
      });
    });
  }
  
  setupPeriodicTasks() {
    // Set up alarm for periodic tasks (like checking for price drops)
    chrome.alarms.create('periodicCheck', {
      delayInMinutes: 1,
      periodInMinutes: 60 // Check every hour
    });
    
    chrome.alarms.onAlarm.addListener((alarm) => {
      if (alarm.name === 'periodicCheck') {
        this.performPeriodicCheck();
      }
    });
  }
  
  async performPeriodicCheck() {
    // This could be used to sync with BargainHawk API
    // or perform other periodic maintenance tasks
    console.log('Performing periodic check...');
    
    try {
      const authStatus = await this.checkAuthStatus();
      if (authStatus.isAuthenticated) {
        // Could check for price drop notifications here
        // or sync user data
      }
    } catch (error) {
      console.error('Error in periodic check:', error);
    }
  }
  
  showWelcomeMessage() {
    this.showNotification(
      'Welcome to BargainHawk!',
      'Extension installed successfully. Visit a retailer website to start tracking prices.',
      'success'
    );
  }
  
  openPopup(tab) {
    // This is automatically handled by Chrome when user clicks the extension icon
    // But we can add custom logic here if needed
    console.log('Opening popup for tab:', tab?.url);
  }
}

// Initialize background script
new BargainHawkBackground();
