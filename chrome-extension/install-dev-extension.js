// Development installation helper for Chrome Extension
const fs = require('fs');
const path = require('path');

class DevInstaller {
  constructor() {
    this.extensionDir = __dirname;
    this.distDir = path.join(__dirname, 'dist');
  }
  
  async install() {
    console.log('🚀 Setting up BargainHawk Extension for Development...\n');
    
    try {
      await this.createDistribution();
      await this.generateIcons();
      await this.validateExtension();
      this.printInstallInstructions();
      
      console.log('✅ Development setup complete!\n');
      return true;
    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      return false;
    }
  }
  
  async createDistribution() {
    console.log('📦 Creating distribution build...');
    
    // Clean and create dist directory
    if (fs.existsSync(this.distDir)) {
      fs.rmSync(this.distDir, { recursive: true });
    }
    fs.mkdirSync(this.distDir);
    
    // Copy all necessary files
    const filesToCopy = [
      'manifest.json',
      'background.js',
      'content-script.js',
      'popup.html',
      'popup.js',
      'popup.css',
      'content-styles.css'
    ];
    
    filesToCopy.forEach(file => {
      const srcPath = path.join(this.extensionDir, file);
      const destPath = path.join(this.distDir, file);
      
      if (fs.existsSync(srcPath)) {
        fs.copyFileSync(srcPath, destPath);
        console.log(`   ✅ Copied ${file}`);
      } else {
        console.log(`   ⚠️  Missing ${file}`);
      }
    });
    
    // Copy icons directory if it exists
    const iconsDir = path.join(this.extensionDir, 'icons');
    const distIconsDir = path.join(this.distDir, 'icons');
    
    if (fs.existsSync(iconsDir)) {
      fs.mkdirSync(distIconsDir);
      const iconFiles = fs.readdirSync(iconsDir);
      iconFiles.forEach(file => {
        if (file.endsWith('.png') || file.endsWith('.svg')) {
          fs.copyFileSync(
            path.join(iconsDir, file),
            path.join(distIconsDir, file)
          );
        }
      });
      console.log(`   ✅ Copied ${iconFiles.length} icon files`);
    }
  }
  
  async generateIcons() {
    console.log('🎨 Generating extension icons...');
    
    const iconsDir = path.join(this.distDir, 'icons');
    if (!fs.existsSync(iconsDir)) {
      fs.mkdirSync(iconsDir);
    }
    
    // Create simple SVG icons if they don't exist
    const sizes = [16, 32, 48, 128];
    
    sizes.forEach(size => {
      const iconPath = path.join(iconsDir, `icon${size}.png`);
      const svgPath = path.join(iconsDir, `icon${size}.svg`);
      
      if (!fs.existsSync(iconPath) && !fs.existsSync(svgPath)) {
        // Create SVG icon
        const svgContent = this.createSVGIcon(size);
        fs.writeFileSync(svgPath, svgContent);
        console.log(`   ✅ Generated icon${size}.svg`);
      }
    });
    
    console.log('   💡 For production, convert SVG icons to PNG format');
  }
  
  createSVGIcon(size) {
    return `<svg width="${size}" height="${size}" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="24" cy="24" r="22" fill="url(#gradient)" stroke="#1e40af" stroke-width="2"/>
  <path d="M12 16 L28 16 L32 24 L28 32 L12 32 Z" fill="white" opacity="0.9"/>
  <text x="20" y="28" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1e40af">$</text>
  <path d="M34 18 L38 22 L34 26 L36 24 Z" fill="white" opacity="0.8"/>
  <circle cx="14" cy="20" r="1" fill="#1e40af"/>
  <circle cx="14" cy="24" r="1" fill="#1e40af"/>
  <circle cx="14" cy="28" r="1" fill="#1e40af"/>
</svg>`;
  }
  
  async validateExtension() {
    console.log('🔍 Validating extension...');
    
    const manifestPath = path.join(this.distDir, 'manifest.json');
    if (!fs.existsSync(manifestPath)) {
      throw new Error('manifest.json not found in dist directory');
    }
    
    try {
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
      console.log(`   ✅ Extension: ${manifest.name} v${manifest.version}`);
      console.log(`   ✅ Manifest version: ${manifest.manifest_version}`);
      
      // Check required files exist
      const requiredFiles = ['background.js', 'content-script.js', 'popup.html'];
      requiredFiles.forEach(file => {
        if (fs.existsSync(path.join(this.distDir, file))) {
          console.log(`   ✅ ${file} present`);
        } else {
          throw new Error(`Required file missing: ${file}`);
        }
      });
      
    } catch (error) {
      throw new Error(`Invalid manifest: ${error.message}`);
    }
  }
  
  printInstallInstructions() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 CHROME EXTENSION INSTALLATION INSTRUCTIONS');
    console.log('='.repeat(60));
    
    console.log('\n🔧 DEVELOPMENT INSTALLATION:');
    console.log('1. Open Chrome and go to: chrome://extensions/');
    console.log('2. Enable "Developer mode" (toggle in top right)');
    console.log('3. Click "Load unpacked"');
    console.log(`4. Select this folder: ${this.distDir}`);
    console.log('5. The extension should now appear in your extensions list');
    
    console.log('\n🧪 TESTING:');
    console.log('1. Visit a supported retailer website:');
    console.log('   • https://costco.ca (any product page)');
    console.log('   • https://walmart.ca (any product page)');
    console.log('   • https://wayfair.ca (any product page)');
    console.log('2. Click the BargainHawk extension icon in the toolbar');
    console.log('3. Login with your BargainHawk account');
    console.log('4. Try the "Auto-Detect Product" or "Start Price Selection" features');
    
    console.log('\n🔄 DEVELOPMENT WORKFLOW:');
    console.log('1. Make changes to source files in chrome-extension/');
    console.log('2. Run: npm run build');
    console.log('3. Go to chrome://extensions/ and click the refresh icon on your extension');
    console.log('4. Test your changes');
    
    console.log('\n⚙️  CONFIGURATION:');
    console.log('• Update API endpoint in popup.js and background.js');
    console.log('• Modify retailer configurations in content-script.js');
    console.log('• Customize styling in popup.css and content-styles.css');
    
    console.log('\n🚀 PRODUCTION DEPLOYMENT:');
    console.log('1. Run: npm run package');
    console.log('2. Upload the generated ZIP file to Chrome Web Store');
    console.log('3. Follow Chrome Web Store publishing guidelines');
    
    console.log('\n📁 Files created in dist/:');
    if (fs.existsSync(this.distDir)) {
      const files = fs.readdirSync(this.distDir, { withFileTypes: true });
      files.forEach(file => {
        if (file.isDirectory()) {
          const subFiles = fs.readdirSync(path.join(this.distDir, file.name));
          console.log(`   📂 ${file.name}/ (${subFiles.length} files)`);
        } else {
          const stats = fs.statSync(path.join(this.distDir, file.name));
          const sizeKB = Math.round(stats.size / 1024);
          console.log(`   📄 ${file.name} (${sizeKB}KB)`);
        }
      });
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 Ready to install and test your extension!');
    console.log('='.repeat(60));
  }
}

// Run installation
const installer = new DevInstaller();
installer.install().then(success => {
  process.exit(success ? 0 : 1);
});
