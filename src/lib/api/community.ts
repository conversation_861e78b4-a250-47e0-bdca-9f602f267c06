import { fetchWithAuth } from '../utils/api';

export interface UserProfile {
  supabaseUserId: string;
  username: string;
  firstName?: string;
  lastName?: string;
  email: string;
  totalPosts: number;
  totalComments: number;
  totalUpvotes: number;
  totalDownvotes: number;
  reputationScore: number;
  createdAt: string;
  updatedAt: string;
}

export interface CommunityPost {
  id: string;
  authorId: string;
  authorUsername: string;
  title: string;
  content: string;
  productUrl?: string;
  productName?: string;
  productImage?: string;
  originalPrice?: number;
  currentPrice?: number;
  store: string;
  category: string;
  createdAt: string;
  updatedAt: string;
  upvotes: number;
  downvotes: number;
  commentCount: number;
  viewCount: number;
  isFeatured: boolean;
  tags: string[];
  hasUpvoted: boolean;
  hasDownvoted: boolean;
}

export interface CommunityComment {
  id: string;
  postId: string;
  authorId: string;
  authorUsername: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  upvotes: number;
  downvotes: number;
  parentCommentId?: string;
  depth: number;
  hasUpvoted: boolean;
  hasDownvoted: boolean;
  replies?: CommunityComment[];
}

export interface CreateUserProfileRequest {
  username: string;
  firstName?: string;
  lastName?: string;
}

export interface CreatePostRequest {
  title: string;
  content: string;
  productUrl?: string;
  productName?: string;
  productImage?: string;
  originalPrice?: number;
  currentPrice?: number;
  store: string;
  category: string;
}

export interface CreateCommentRequest {
  content: string;
  parentCommentId?: string;
}

// User Profile API
export async function createUserProfile(request: CreateUserProfileRequest): Promise<UserProfile> {
  const response = await fetchWithAuth('api/community/profile', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(request)
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to create user profile');
  }

  return response.json();
}

export async function getUserProfile(): Promise<UserProfile | null> {
  try {
    const response = await fetchWithAuth('api/community/profile');
    if (response.status === 404) return null;
    if (!response.ok) throw new Error('Failed to get user profile');
    return response.json();
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
}

export async function checkUserProfileExists(): Promise<boolean> {
  try {
    const response = await fetchWithAuth('api/community/profile/exists');
    if (!response.ok) return false;
    const data = await response.json();
    return data.exists;
  } catch (error) {
    console.error('Error checking user profile:', error);
    return false;
  }
}

// Posts API
export async function createPost(request: CreatePostRequest): Promise<CommunityPost> {
  const response = await fetchWithAuth('api/community/posts', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(request)
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to create post');
  }

  return response.json();
}

export async function getPosts(params: {
  page?: number;
  size?: number;
  store?: string;
  category?: string;
  sort?: string;
} = {}): Promise<{ content: CommunityPost[]; totalElements: number; totalPages: number }> {
  const searchParams = new URLSearchParams();
  if (params.page !== undefined) searchParams.set('page', params.page.toString());
  if (params.size !== undefined) searchParams.set('size', params.size.toString());
  if (params.store) searchParams.set('store', params.store);
  if (params.category) searchParams.set('category', params.category);
  if (params.sort) searchParams.set('sort', params.sort);

  const response = await fetchWithAuth(`api/public/community/posts?${searchParams.toString()}`);

  if (!response.ok) {
    throw new Error('Failed to get posts');
  }

  return response.json();
}

export async function getPost(postId: string): Promise<CommunityPost> {
  const response = await fetchWithAuth(`api/public/community/posts/${postId}`);

  if (!response.ok) {
    throw new Error('Failed to get post');
  }

  return response.json();
}

export async function upvotePost(postId: string): Promise<CommunityPost> {
  const response = await fetchWithAuth(`api/community/posts/${postId}/upvote`, {
    method: 'POST'
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to upvote post');
  }

  return response.json();
}

export async function downvotePost(postId: string): Promise<CommunityPost> {
  const response = await fetchWithAuth(`api/community/posts/${postId}/downvote`, {
    method: 'POST'
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to downvote post');
  }

  return response.json();
}

// Comments API
export async function createComment(postId: string, request: CreateCommentRequest): Promise<CommunityComment> {
  const response = await fetchWithAuth(`api/community/posts/${postId}/comments`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(request)
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to create comment');
  }

  return response.json();
}

export async function getComments(postId: string): Promise<CommunityComment[]> {
  const response = await fetchWithAuth(`api/public/community/comments/${postId}`);

  if (!response.ok) {
    throw new Error('Failed to get comments');
  }

  return response.json();
}

export async function upvoteComment(commentId: string): Promise<CommunityComment> {
  const response = await fetchWithAuth(`api/community/comments/${commentId}/upvote`, {
    method: 'POST'
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to upvote comment');
  }

  return response.json();
}

export async function downvoteComment(commentId: string): Promise<CommunityComment> {
  const response = await fetchWithAuth(`api/community/comments/${commentId}/downvote`, {
    method: 'POST'
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to downvote comment');
  }

  return response.json();
}
