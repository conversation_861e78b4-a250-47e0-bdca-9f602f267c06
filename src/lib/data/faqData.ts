export interface FAQ {
  question: string;
  answer: string;
  category: 'general' | 'pricing' | 'tracking' | 'usage';
}

export const faqs: FAQ[] = [
  {
    question: "Are you affiliated with Costco?",
    answer: "No, BargainHawk is not affiliated with Costco in any way. We are an independent service created to help shoppers save money by tracking prices and facilitating price adjustment claims.",
    category: "general"
  },
  {
    question: "How does Costco's price adjustment policy work?",
    answer: "Costco offers price adjustments within 30 days of purchase. If an item you bought goes on sale within this period, you can request a refund for the difference.",
    category: "pricing"
  },
  {
    question: "How do I track items with multiple variations?",
    answer: "For products with multiple variations (like different sizes or colors), always enter the lowest price from the available options. The product URL doesn't always reflect the specific variation, so this ensures accurate tracking.",
    category: "tracking"
  },
  {
    question: "How does BargainHawk help with price adjustments?",
    answer: "We automatically track prices of your items and notify you immediately when prices drop. This helps you claim price adjustments within the 30-day window.",
    category: "usage"
  },
  {
    question: "How do I claim a price adjustment?",
    answer: "When you receive a price drop notification, visit any Costco warehouse with your original receipt or contact customer service if purchased online.",
    category: "usage"
  },
  {
    question: "What items can I track?",
    answer: "Currently, we support tracking any product available on Costco (costco.ca and costco.com) and Wayfair (wayfair.ca and wayfair.com). For products with multiple variations, make sure to enter the lowest price option.",
    category: "tracking"
  },
  {
    question: "Is this service free?",
    answer: "Yes, BargainHawk is completely free to use. Simply create an account and start tracking your purchases.",
    category: "general"
  }
];