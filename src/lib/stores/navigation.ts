import { writable } from 'svelte/store';

export type Page = 'dashboard' | 'about' | 'faq' | 'privacy' | 'terms' | 'blog' | 'feed' | 'flyers' | 'price-history' | 'reset-password' | 'forgot-password' | 'auth' | 'home' | string;
export const page = writable<Page>('home');

function scrollToTop() {
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: 'smooth'
  });
}

function updateBrowserHistory(path: string) {
  if (window.location.pathname !== path) {
    window.history.pushState({}, '', path);
  }
}

function initializePageFromUrl() {
  const path = window.location.pathname;
  if (path.startsWith('/blog/')) {
    page.set(`blog/${path.slice(6)}`);
    return;
  }
  
  const routes: Record<string, Page> = {
    '/dashboard': 'dashboard',
    '/about': 'about',
    '/faq': 'faq',
    '/privacy': 'privacy',
    '/terms': 'terms',
    '/blog': 'blog',
    '/feed': 'feed',
    '/flyers': 'flyers',
    '/price-history': 'price-history',
    '/reset-password': 'reset-password',
    '/forgot-password': 'forgot-password',
    '/auth': 'auth',
    '/': 'home'
  };
  
  const newPage = routes[path] || 'home';
  page.set(newPage);
}

function setupNavigationHandlers() {
  page.subscribe(currentPage => {
    let newPath: string;
    if (currentPage.startsWith('blog/')) {
      newPath = `/blog/${currentPage.slice(5)}`;
    } else {
      newPath = currentPage === 'home' ? '/' : `/${currentPage}`;
    }
    updateBrowserHistory(newPath);
    scrollToTop();
  });

  window.addEventListener('popstate', () => {
    initializePageFromUrl();
    scrollToTop();
  });
}

initializePageFromUrl();
setupNavigationHandlers();