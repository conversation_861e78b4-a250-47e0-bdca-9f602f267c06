import { writable } from 'svelte/store';
import { supabase } from '../utils/supabase';
import type { User } from '@supabase/supabase-js';
import { page } from './navigation';

export const user = writable<User | null>(null);
export const authError = writable<string | null>(null);

// Initialize auth state
supabase.auth.onAuthStateChange((event, session) => {
  const currentUser = session?.user ?? null;
  user.set(currentUser);
  
  // Only navigate on explicit sign in/out events, not on session refresh
  if (event === 'SIGNED_IN') {
    authError.set(null);
    // Don't automatically redirect to dashboard - let users stay on current page
  } else if (event === 'SIGNED_OUT') {
    authError.set(null);
    page.set('home');
  }
});

export async function signIn(email: string, password: string) {
  try {
    authError.set(null);
    const { data, error } = await supabase.auth.signInWithPassword({ 
      email, 
      password 
    });
    
    if (error) {
      if (error.message === 'Invalid login credentials') {
        throw new Error('Invalid email or password. Please try again.');
      }
      throw error;
    }
    
    return data;
  } catch (e) {
    const message = e instanceof Error ? e.message : 'Failed to sign in';
    authError.set(message);
    throw e;
  }
}

export async function signUp(email: string, password: string) {
  try {
    authError.set(null);
    const { data, error } = await supabase.auth.signUp({ 
      email, 
      password,
      options: {
        emailRedirectTo: window.location.origin
      }
    });
    
    if (error) throw error;
    return data;
  } catch (e) {
    const message = e instanceof Error ? e.message : 'Failed to create account';
    authError.set(message);
    throw e;
  }
}

export async function signOut() {
  try {
    authError.set(null);

    // Try global logout first, but fallback to local if session is invalid
    let { error } = await supabase.auth.signOut({ scope: 'global' });

    // If global logout fails due to session issues, do local logout
    if (error && (error.message.includes('session_not_found') || error.message.includes('Auth session missing'))) {
      console.log('Global logout failed, doing local logout:', error.message);
      const localResult = await supabase.auth.signOut({ scope: 'local' });
      error = localResult.error;
    }

    // Only throw if it's not a session-related error
    if (error && !error.message.includes('session_not_found') && !error.message.includes('Auth session missing')) {
      throw error;
    }

  } catch (e) {
    // For any session-related errors, just do local cleanup
    if (e instanceof Error && (e.message.includes('session_not_found') || e.message.includes('Auth session missing'))) {
      console.log('Doing local cleanup due to session error:', e.message);
      await supabase.auth.signOut({ scope: 'local' });
      return;
    }

    const message = e instanceof Error ? e.message : 'Failed to sign out';
    authError.set(message);
    throw e;
  }
}