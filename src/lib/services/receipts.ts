import { fetchWithAuth } from '../utils/api';
import type { UploadMetadata, ReceiptData } from '../types/receipt';
import { user } from '../stores/auth';
import { get } from 'svelte/store';

export async function getUserReceipts(): Promise<UploadMetadata[]> {
  const currentUser = get(user);
  if (!currentUser) {
    throw new Error('User not authenticated');
  }

  try {
    const response = await fetchWithAuth(`uploads/user/${currentUser.id}`);
    
    if (!response.ok) {
      if (response.status === 204) {
        return [];
      }
      throw new Error('Failed to fetch receipts');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching receipts:', error);
    throw new Error('Failed to load receipts');
  }
}

export async function getReceiptData(receiptId: string): Promise<ReceiptData | null> {
  try {
    const response = await fetchWithAuth(`receipts/${receiptId}`);
    
    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error('Failed to fetch receipt data');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching receipt data:', error);
    throw new Error('Failed to load receipt data');
  }
}