import { API_URL, isDevelopment } from './env';
import { supabase } from './supabase';

export async function fetchWithAuth(url: string, options: RequestInit = {}): Promise<Response> {
  // Get the current session
  const { data: { session } } = await supabase.auth.getSession();
  
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...options.headers,
  };

  // Add Authorization header if we have a session
  if (session?.access_token) {
    headers['Authorization'] = `Bearer ${session.access_token}`;
  }

  // Remove any leading slash for consistency
  const cleanUrl = url.startsWith('/') ? url.slice(1) : url;

  // In development, use the proxy, in production use the full API URL
  const baseUrl = isDevelopment ? '/api' : API_URL;
  const fullUrl = `${baseUrl}/${cleanUrl}`;

  const response = await fetch(fullUrl, {
    ...options,
    headers,
    credentials: 'include',
  });

  // Handle 401 Unauthorized responses
  if (response.status === 401) {
    // Check if the session is still valid
    const { data: { session: currentSession } } = await supabase.auth.getSession();
    if (!currentSession) {
      // Session is invalid, throw an error to be handled by the calling code
      throw new Error('Your session has expired. Please sign in again.');
    }
  }

  // Log failed requests in development
  if (!response.ok && import.meta.env.DEV) {
    console.error(`API request failed: ${url.toString()}`, {
      status: response.status,
      statusText: response.statusText,
    });
  }

  return response;
}