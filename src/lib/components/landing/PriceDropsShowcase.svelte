<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { fade, fly } from 'svelte/transition';
  import { TrendingDown, ArrowRight, ExternalLink } from 'lucide-svelte';
  import { formatPrice, formatTimeAgo } from '../../utils/format';
  import { getPriceChanges } from '../../services/priceChange';
  import { page } from '../../stores/navigation';
  import type { PriceChange } from '../../types/priceChange';

  let priceDrops: PriceChange[] = [];
  let loading = true;
  let scrollContainer: HTMLElement;
  let autoScrollInterval: ReturnType<typeof setInterval>;
  let isContainerHovered = false;
  let isItemHovered = false;
  let resumeTimeout: ReturnType<typeof setTimeout>;

  async function loadPriceDrops() {
    try {
      const allPriceChanges = await getPriceChanges();
      // Filter to show only price drops and limit to 10 most recent
      priceDrops = allPriceChanges
        .filter(change => change.newPrice < change.previousPrice)
        .slice(0, 10);
    } catch (error) {
      console.error('Failed to load price drops:', error);
      // Use mock data if API fails
      priceDrops = [];
    } finally {
      loading = false;
    }
  }

  function startAutoScroll() {
    if (!scrollContainer || priceDrops.length === 0) return;

    autoScrollInterval = setInterval(() => {
      if (!isContainerHovered && !isItemHovered && scrollContainer) {
        const scrollWidth = scrollContainer.scrollWidth;
        const clientWidth = scrollContainer.clientWidth;
        const currentScroll = scrollContainer.scrollLeft;

        // If we've reached the end, scroll back to start
        if (currentScroll >= scrollWidth - clientWidth - 10) {
          scrollContainer.scrollTo({ left: 0, behavior: 'auto' });
        } else {
          // Scroll by 5 pixels for faster smooth continuous motion
          scrollContainer.scrollBy({ left: 5, behavior: 'auto' });
        }
      }
    }, 20); // Update every 20ms for smooth 50fps scrolling
  }

  function stopAutoScroll() {
    if (autoScrollInterval) {
      clearInterval(autoScrollInterval);
    }
  }

  function clearResumeTimeout() {
    if (resumeTimeout) {
      clearTimeout(resumeTimeout);
    }
  }

  function handleContainerMouseEnter() {
    isContainerHovered = true;
    clearResumeTimeout();
  }

  function handleContainerMouseLeave() {
    isContainerHovered = false;
    // Resume auto-scroll after a short delay
    clearResumeTimeout();
    resumeTimeout = setTimeout(() => {
      // Double-check that user isn't still hovering over an item
      if (!isItemHovered) {
        isContainerHovered = false;
      }
    }, 1000); // Resume after 1 second
  }

  function handleItemMouseEnter() {
    isItemHovered = true;
    clearResumeTimeout();
  }

  function handleItemMouseLeave() {
    isItemHovered = false;
    // Resume auto-scroll after a longer delay when leaving an item
    clearResumeTimeout();
    resumeTimeout = setTimeout(() => {
      if (!isContainerHovered) {
        isItemHovered = false;
      }
    }, 2000); // Resume after 2 seconds when leaving an item
  }

  function handleCardClick(priceChange: PriceChange) {
    window.open(priceChange.url, '_blank', 'noopener,noreferrer');
  }

  function goToPriceDrops() {
    page.set('feed');
  }

  onMount(() => {
    loadPriceDrops();
  });

  $: if (scrollContainer && priceDrops.length > 0 && !loading) {
    // Start auto-scroll after a short delay
    setTimeout(startAutoScroll, 1000);
  }

  onDestroy(() => {
    stopAutoScroll();
    clearResumeTimeout();
  });
</script>

<section class="py-16 md:py-20 gradient-bg border-t border-gray-800/50 overflow-hidden">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="text-center mb-8 md:mb-12" in:fade={{ duration: 600 }}>
      <div class="flex items-center justify-center gap-2 mb-4">
        <TrendingDown class="w-6 h-6 text-green-400" />
        <h2 class="text-2xl md:text-3xl font-bold text-white">
          Live Price Drops
        </h2>
      </div>
      <p class="text-lg text-gray-300 max-w-2xl mx-auto">
        See real-time price drops happening right now. Join thousands of smart shoppers saving money daily.
      </p>
    </div>

    {#if loading}
      <!-- Loading skeleton -->
      <div class="flex gap-4 overflow-hidden">
        {#each Array(4) as _}
          <div class="flex-shrink-0 w-80 h-96 bg-dark-lighter/30 rounded-xl animate-pulse border border-gray-800/50"></div>
        {/each}
      </div>
    {:else if priceDrops.length === 0}
      <!-- No data state -->
      <div class="text-center py-12 bg-dark-lighter/30 rounded-xl border border-gray-800/50">
        <TrendingDown class="w-12 h-12 text-gray-500 mx-auto mb-4" />
        <h3 class="text-xl font-medium text-white mb-2">No Recent Price Drops</h3>
        <p class="text-gray-400">Check back soon for new deals!</p>
      </div>
    {:else}
      <!-- Price drops carousel -->
      <div class="relative">
        <!-- Gradient overlays for scroll indication -->
        <div class="absolute left-0 top-0 bottom-4 w-8 bg-gradient-to-r from-dark-bg to-transparent z-10 pointer-events-none"></div>
        <div class="absolute right-0 top-0 bottom-4 w-8 bg-gradient-to-l from-dark-bg to-transparent z-10 pointer-events-none"></div>

        <div
          bind:this={scrollContainer}
          class="flex gap-4 overflow-x-auto scrollbar-hide pb-4 scroll-smooth px-2"
          style="scrollbar-width: none; -ms-overflow-style: none;"
          on:mouseenter={handleContainerMouseEnter}
          on:mouseleave={handleContainerMouseLeave}
          role="region"
          aria-label="Price drops carousel"
        >
          {#each priceDrops as priceChange, i (priceChange.id)}
            <div
              class="flex-shrink-0 w-80 bg-dark-lighter/50 rounded-xl border border-gray-800/50
                     hover:border-gray-600 transition-all duration-300 cursor-pointer group
                     backdrop-blur-sm hover:shadow-lg hover:shadow-primary/10"
              on:click={() => handleCardClick(priceChange)}
              on:keydown={(e) => e.key === 'Enter' && handleCardClick(priceChange)}
              on:mouseenter={handleItemMouseEnter}
              on:mouseleave={handleItemMouseLeave}
              role="button"
              tabindex="0"
              in:fly={{ x: 50, duration: 400, delay: i * 100 }}
            >
              <!-- Product Image -->
              <div class="relative h-48 overflow-hidden rounded-t-xl bg-dark/50">
                <img
                  src={priceChange.imageUrl}
                  alt="Product"
                  class="w-full h-full object-contain transform transition-transform duration-300 
                         group-hover:scale-105"
                  loading="lazy"
                />
                <div class="absolute inset-0 bg-gradient-to-t from-dark/60 to-transparent opacity-0 
                           group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <ExternalLink class="w-6 h-6 text-white" />
                </div>
              </div>

              <!-- Content -->
              <div class="p-4 space-y-3">
                <!-- Time ago -->
                <div class="text-xs text-gray-500">
                  {formatTimeAgo(priceChange.createdDate)}
                </div>

                <!-- Price comparison -->
                <div class="space-y-2">
                  <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-400">Was:</span>
                    <span class="text-gray-500 line-through">
                      {formatPrice(priceChange.previousPrice)}
                    </span>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-400">Now:</span>
                    <span class="text-xl font-bold text-primary">
                      {formatPrice(priceChange.newPrice)}
                    </span>
                  </div>
                </div>

                <!-- Savings badge -->
                <div class="bg-green-900/30 border border-green-500/30 rounded-lg p-3 text-center">
                  <div class="flex items-center justify-center gap-2">
                    <TrendingDown class="w-4 h-4 text-green-400" />
                    <span class="text-green-400 font-semibold text-sm">
                      Save {formatPrice(priceChange.previousPrice - priceChange.newPrice)}
                    </span>
                  </div>
                  <div class="text-xs text-green-300 mt-1">
                    {(((priceChange.previousPrice - priceChange.newPrice) / priceChange.previousPrice) * 100).toFixed(1)}% off
                  </div>
                </div>
              </div>
            </div>
          {/each}
        </div>

        <!-- View All Button -->
        <div class="text-center mt-8" in:fade={{ duration: 600, delay: 400 }}>
          <button
            class="inline-flex items-center gap-2 bg-primary hover:bg-primary-dark 
                   text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300
                   hover:shadow-lg hover:shadow-primary/25 transform hover:-translate-y-0.5"
            on:click={goToPriceDrops}
          >
            View All Price Drops
            <ArrowRight class="w-4 h-4" />
          </button>
        </div>
      </div>
    {/if}
  </div>
</section>

<style>
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
</style>
