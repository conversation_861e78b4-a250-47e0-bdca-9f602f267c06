  <script lang="ts">
    import { fade, fly } from 'svelte/transition';
    import { <PERSON><PERSON>hart, BellRing, Clock, DollarSign, ShoppingCart, Shield } from 'lucide-svelte';

    const features = [
      {
        icon: LineChart,
        title: 'Price History Tracking',
        description: 'View detailed price history charts and analyze trends to make informed purchasing decisions.'
      },
      {
        icon: BellRing,
        title: 'Instant Notifications',
        description: 'Get real-time alerts when prices drop on your tracked items, never miss a deal again.'
      },
      {
        icon: Clock,
        title: '30-Day Window Tracking',
        description: 'Automatically monitor price adjustments within Costco\'s 30-day price protection window.'
      },
      {
        icon: DollarSign,
        title: 'Savings Calculator',
        description: 'See potential savings and price adjustment amounts at a glance.'
      },
      {
        icon: ShoppingCart,
        title: 'Multi-Store Support',
        description: 'Track prices across Costco and Wayfair, with more retailers coming soon.'
      },
      {
        icon: Shield,
        title: 'Privacy First',
        description: 'Your data is secure and private. We never share your information with third parties.'
      }
    ];
  </script>

  <section class="py-16 md:py-24 gradient-bg border-t border-gray-800/50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12 md:mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-100">
          Everything you need to save money
        </h2>
        <p class="mt-4 text-lg md:text-xl text-gray-300">
          Powerful features to help you track prices
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
        {#each features as feature, i}
          <div 
            class="p-6 bg-dark-lighter/50 backdrop-blur-sm rounded-xl border border-gray-700/50
                  hover:border-gray-600 transition-all duration-300"
            in:fly={{ y: 20, duration: 400, delay: i * 100 }}
          >
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
              <svelte:component this={feature.icon} class="w-6 h-6 text-primary" />
            </div>
            <h3 class="text-lg md:text-xl font-semibold text-gray-100 mb-2">
              {feature.title}
            </h3>
            <p class="text-sm md:text-base text-gray-300">
              {feature.description}
            </p>
          </div>
        {/each}
      </div>
    </div>
  </section>