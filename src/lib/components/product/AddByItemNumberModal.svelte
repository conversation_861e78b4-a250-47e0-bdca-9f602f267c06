<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import Modal from '../ui/Modal.svelte';
    import Button from '../ui/Button.svelte';
    import Input from '../ui/Input.svelte';
    import { CircleHelp as HelpCircle } from 'lucide-svelte';
    import { fetchWithAuth } from '../../utils/api';
    import { DotLottieSvelte } from '@lottiefiles/dotlottie-svelte';
    
    export let show = false;
    
    const dispatch = createEventDispatcher();
    let store: 'ca' | 'us' = 'ca';
    let itemNumber = '';
    let price = '';
    let error = '';
    let loading = false;
    let loadingMessage = 'Processing...';
  
    function handlePriceInput(event: Event) {
      const input = event.target as HTMLInputElement;
      let value = input.value.replace(/[^\d.]/g, '');
      
      const parts = value.split('.');
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
      }
      
      if (parts[1]?.length > 2) {
        value = parts[0] + '.' + parts[1].slice(0, 2);
      }
      
      price = value;
    }
  
    async function handleSubmit() {
      if (!itemNumber.trim()) {
        error = 'Please enter an item number';
        return;
      }
  
      const priceNum = Number(price);
      if (!price || isNaN(priceNum) || priceNum <= 0) {
        error = 'Please enter a valid target price';
        return;
      }
  
      try {
        loading = true;
        error = '';
        
        loadingMessage = 'Fetching product details...';
        
        const response = await fetchWithAuth('fetchByItem', {
          method: 'POST',
          body: JSON.stringify({
            storeAndCountryCode: store === 'us' ? 'COSTCOUSA' : 'COSTCOCANADA',
            itemNumber: itemNumber.trim(),
            price: priceNum
          })
        });
  
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(errorText || 'Failed to add product');
        }
  
        loadingMessage = 'Almost done...';
        await new Promise(resolve => setTimeout(resolve, 1000));
  
        dispatch('success');
        handleClose();
      } catch (e) {
        error = e instanceof Error ? e.message : 'Failed to add product';
      } finally {
        loading = false;
        loadingMessage = 'Processing...';
      }
    }
  
    function handleClose() {
      itemNumber = '';
      price = '';
      error = '';
      store = 'ca';
      dispatch('close');
    }
  </script>
  
  <Modal {show} on:click={handleClose}>
    {#if loading}
      <div class="p-6 text-center">
        <div class="w-48 h-48 mx-auto mb-4">
          <DotLottieSvelte
            src="https://lottie.host/86744b6f-2886-4713-9d8b-51a734d40130/vB9JJGcex5.lottie"
            loop
            autoplay
          />
        </div>
        <h3 class="text-xl font-semibold text-white mb-2">{loadingMessage}</h3>
        <p class="text-gray-400 text-sm">Please wait while we process your request...</p>
      </div>
    {:else}
      <div class="p-6">
        <h2 class="text-2xl font-bold text-white mb-6">Add By Item Number</h2>
        
        <form on:submit|preventDefault={handleSubmit} class="space-y-4">
          <div class="block">
            <span class="text-sm font-medium text-gray-300">Store</span>
            <div class="mt-1.5">
              <select
                bind:value={store}
                class="block w-full px-4 py-3 rounded-lg bg-dark border border-gray-700 
                       text-white text-base appearance-none
                       focus:border-primary focus:ring-1 focus:ring-primary
                       hover:border-gray-600 transition-colors"
              >
                <optgroup label="Available">
                  <option value="ca">Costco Canada</option>
                  <option value="us">Costco USA</option>
                </optgroup>
                <optgroup label="Coming Soon" class="text-gray-500">
                  <option value="sameday-ca" disabled>Sameday Costco Canada (Coming Soon)</option>
                  <option value="sameday-us" disabled>Sameday Costco USA (Coming Soon)</option>
                </optgroup>
              </select>
            </div>
          </div>
  
          <Input
            label="Item Number"
            bind:value={itemNumber}
            required
            placeholder="Enter Costco item number"
          />
          
          <div class="block">
            <div class="flex items-center gap-2 mb-1.5">
              <span class="text-sm font-medium text-gray-300">Target Price</span>
              <div class="relative group">
                <HelpCircle class="w-4 h-4 text-gray-400 cursor-help" />
                <div class="invisible group-hover:visible absolute left-1/2 -translate-x-1/2 bottom-full mb-2 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg z-50">
                  Enter the price you want to pay. We'll notify you when the price drops below this amount.
                  <div class="absolute left-1/2 -translate-x-1/2 top-full w-2 h-2 bg-gray-800 transform rotate-45"></div>
                </div>
              </div>
            </div>
            <input
              type="text"
              inputmode="decimal"
              bind:value={price}
              on:input={handlePriceInput}
              required
              placeholder="0.00"
              class="block w-full px-4 py-3 rounded-lg bg-dark border border-gray-700 
                     text-white text-base
                     focus:border-primary focus:ring-1 focus:ring-primary
                     hover:border-gray-600 transition-colors"
            />
          </div>
          
          {#if error}
            <p class="text-red-400 text-sm">{error}</p>
          {/if}
          
          <div class="flex justify-end gap-3 pt-2">
            <Button variant="secondary" on:click={handleClose} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              Add Product
            </Button>
          </div>
        </form>
      </div>
    {/if}
  </Modal>
  
  <style>
    optgroup option:disabled {
      color: rgb(107, 114, 128); /* text-gray-500 */
      font-style: italic;
    }
  </style>