<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import Modal from '../ui/Modal.svelte';
    import { Receipt, Link2, Hash } from 'lucide-svelte';
    
    export let show = false;
    
    const dispatch = createEventDispatcher();
  
    function handleClose() {
      dispatch('close');
    }
  
    function handleAddByReceipt() {
      dispatch('addByReceipt');
    }
  
    function handleAddByUrl() {
      dispatch('addByUrl');
    }
  
    function handleAddByItemNumber() {
      dispatch('addByItemNumber');
    }
  </script>
  
  <Modal {show} on:click={handleClose}>
    <div class="p-6">
      <h2 class="text-2xl font-bold text-white mb-6">Add Product</h2>
      
      <div class="space-y-4">
        <!-- Upload Receipt Option -->
        <button
          class="w-full p-4 bg-dark/50 rounded-lg border border-gray-800/50 backdrop-blur-sm
                 hover:bg-dark-lighter/50 hover:border-gray-700 transition-all duration-200
                 flex items-center gap-4"
          on:click={handleAddByReceipt}
        >
          <div class="p-2 bg-primary/10 rounded-lg">
            <Receipt class="w-6 h-6 text-primary" />
          </div>
          <div class="text-left">
            <div class="text-white font-medium">In Beta: Upload Your Costco Receipt</div>
            <div class="text-sm text-gray-400">Track items from your receipt</div>
          </div>
        </button>
  
        <!-- Add by URL Option -->
        <button
          class="w-full p-4 bg-dark/50 rounded-lg border border-gray-800/50 backdrop-blur-sm
                 hover:bg-dark-lighter/50 hover:border-gray-700 transition-all duration-200
                 flex items-center gap-4"
          on:click={handleAddByUrl}
        >
          <div class="p-2 bg-primary/10 rounded-lg">
            <Link2 class="w-6 h-6 text-primary" />
          </div>
          <div class="text-left">
            <div class="text-white font-medium">Add By URL</div>
            <div class="text-sm text-gray-400">Track product using its webpage URL</div>
          </div>
        </button>
  
        <!-- Add by Item Number Option -->
        <button
          class="w-full p-4 bg-dark/50 rounded-lg border border-gray-800/50 backdrop-blur-sm
                 hover:bg-dark-lighter/50 hover:border-gray-700 transition-all duration-200
                 flex items-center gap-4"
          on:click={handleAddByItemNumber}
        >
          <div class="p-2 bg-primary/10 rounded-lg">
            <Hash class="w-6 h-6 text-primary" />
          </div>
          <div class="text-left">
            <div class="text-white font-medium">Add By Item Number</div>
            <div class="text-sm text-gray-400">Track product using Costco item number</div>
          </div>
        </button>
      </div>
    </div>
  </Modal>