<script lang="ts">
    import { Plus } from 'lucide-svelte';
    import { fly } from 'svelte/transition';
    import { createEventDispatcher } from 'svelte';
  
    const dispatch = createEventDispatcher();
    let isHovered = false;
  </script>
  
  <div
    class="bg-dark-lighter/50 rounded-lg border border-gray-800/50 backdrop-blur-sm overflow-hidden
           hover:border-primary/50 transition-all duration-300 h-[400px] flex flex-col cursor-pointer"
    on:mouseenter={() => isHovered = true}
    on:mouseleave={() => isHovered = false}
    on:click={() => dispatch('click')}
    in:fly={{ y: 20, duration: 300 }}
    role="button"
    aria-label="Add new product"
  >
    <div class="relative h-[250px] overflow-hidden bg-dark/50 flex-shrink-0 flex items-center justify-center">
      <div class="transform transition-transform duration-300 {isHovered ? 'scale-110' : ''}">
        <div class="p-6 rounded-full bg-primary/10 border border-primary/20">
          <Plus class="w-12 h-12 text-primary" />
        </div>
      </div>
    </div>
  
    <div class="p-4 flex-grow flex flex-col items-center justify-center text-center">
      <h3 class="font-medium text-white mb-2">
        Add New Product
      </h3>
      <p class="text-sm text-gray-400">
        Track prices and get notified of drops
      </p>
    </div>
  </div>