<script lang="ts">
  import type { Product } from '../../types';
  import ProductCard from './ProductCard.svelte';
  import AddProductCard from './AddProductCard.svelte';
  import AddProductModal from './AddProductModal.svelte';
  import AddProductOptionsModal from './AddProductOptionsModal.svelte';
  import AddByItemNumberModal from './AddByItemNumberModal.svelte';
  import AddReceiptModal from './AddReceiptModal.svelte';
  import CelebrationModal from '../CelebrationModal.svelte';
  import { flip } from 'svelte/animate';
  import { lastAddedProductId } from '../../stores/products';
  
  export let products: Product[];
  export let onDelete: (id: string) => void;
  export let showAddModal = false;
  export let onAddModalClose: () => void;
  export let onAddModalSuccess: () => void;

  let showOptionsModal = false;
  let showUrlModal = false;
  let showItemNumberModal = false;
  let showReceiptModal = false;
  let showCelebration = false;

  function handleAddClick() {
    showOptionsModal = true;
  }

  function handleOptionsClose() {
    showOptionsModal = false;
  }

  function handleAddByUrl() {
    showOptionsModal = false;
    showUrlModal = true;
  }

  function handleAddByItemNumber() {
    showOptionsModal = false;
    showItemNumberModal = true;
  }

  function handleAddByReceipt() {
    showOptionsModal = false;
    showReceiptModal = true;
  }

  function handleProductSuccess() {
    showUrlModal = false;
    showItemNumberModal = false;
    showCelebration = true;
    
    // Dispatch a custom event to notify parent component to refresh
    const event = new CustomEvent('refresh');
    window.dispatchEvent(event);
    
    setTimeout(() => {
      showCelebration = false;
      onAddModalSuccess();
    }, 2000);
  }

  function handleReceiptSuccess() {
    showReceiptModal = false;
    // Dispatch a custom event to notify parent component to refresh
    const event = new CustomEvent('refresh');
    window.dispatchEvent(event);
    onAddModalSuccess();
  }
</script>

<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
  <AddProductCard on:click={handleAddClick} />
  {#each products as product (product.id)}
    <div animate:flip={{ duration: 300 }}>
      <ProductCard {product} {onDelete} />
    </div>
  {/each}
</div>

<AddProductOptionsModal
  show={showOptionsModal}
  on:close={handleOptionsClose}
  on:addByUrl={handleAddByUrl}
  on:addByItemNumber={handleAddByItemNumber}
  on:addByReceipt={handleAddByReceipt}
/>

<AddProductModal
  show={showUrlModal}
  on:close={() => showUrlModal = false}
  on:success={handleProductSuccess}
/>

<AddByItemNumberModal
  show={showItemNumberModal}
  on:close={() => showItemNumberModal = false}
  on:success={handleProductSuccess}
/>

<AddReceiptModal
  show={showReceiptModal}
  on:close={() => showReceiptModal = false}
  on:success={handleReceiptSuccess}
/>

<CelebrationModal
  show={showCelebration}
  on:close={() => showCelebration = false}
/>