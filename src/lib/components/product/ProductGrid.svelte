<script lang="ts">
  import type { Product } from '../../types';
  import ProductCard from './ProductCard.svelte';
  import AddProductCard from './AddProductCard.svelte';
  import AddProductModal from './AddProductModal.svelte';
  import AddProductOptionsModal from './AddProductOptionsModal.svelte';
  import AddByItemNumberModal from './AddByItemNumberModal.svelte';

  import CelebrationModal from '../CelebrationModal.svelte';
  import { flip } from 'svelte/animate';

  
  export let products: Product[];
  export let onDelete: (id: string) => void;


  let showOptionsModal = false;
  let showUrlModal = false;
  let showItemNumberModal = false;

  let showCelebration = false;

  function handleAddClick() {
    showOptionsModal = true;
  }

  function handleOptionsClose() {
    showOptionsModal = false;
  }

  function handleAddByUrl() {
    showOptionsModal = false;
    showUrlModal = true;
  }

  function handleAddByItemNumber() {
    showOptionsModal = false;
    showItemNumberModal = true;
  }



  function handleProductSuccess() {
    showUrlModal = false;
    showItemNumberModal = false;
    showCelebration = true;
    
    // Dispatch a custom event to notify parent component to refresh
    const event = new CustomEvent('refresh');
    window.dispatchEvent(event);
    
    setTimeout(() => {
      showCelebration = false;
    }, 2000);
  }


</script>

<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
  <AddProductCard on:click={handleAddClick} />
  {#each products as product (product.id)}
    <div animate:flip={{ duration: 300 }}>
      <ProductCard {product} {onDelete} />
    </div>
  {/each}
</div>

<AddProductOptionsModal
  show={showOptionsModal}
  on:close={handleOptionsClose}
  on:addByUrl={handleAddByUrl}
  on:addByItemNumber={handleAddByItemNumber}

/>

<AddProductModal
  show={showUrlModal}
  on:close={() => showUrlModal = false}
  on:success={handleProductSuccess}
/>

<AddByItemNumberModal
  show={showItemNumberModal}
  on:close={() => showItemNumberModal = false}
  on:success={handleProductSuccess}
/>



<CelebrationModal
  show={showCelebration}
  on:close={() => showCelebration = false}
/>