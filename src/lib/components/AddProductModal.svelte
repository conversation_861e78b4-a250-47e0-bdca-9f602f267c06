<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Button from './Button.svelte';
  import CloseButton from './CloseButton.svelte';
  import { HelpCircle } from 'lucide-svelte';
  import CelebrationModal from './CelebrationModal.svelte';
  import { user } from '../stores/auth';
  import { lastAddedProductId } from '../stores/products';
  import { isValidUrl, isAllowedDomain, getValidationMessage } from '../utils/validation';
  import { fetchWithAuth } from '../utils/api';
  import { provinces, states } from '../utils/locations';
  
  export let show = false;
  export let initialUrl = '';
  
  const dispatch = createEventDispatcher();
  let url = '';
  let productName = '';
  let price = '';
  let location = '';
  let error = '';
  let loading = false;
  let loadingMessage = 'Fetching product data...';
  let showCelebration = false;

  $: isUSStore = url.includes('costco.com') || url.includes('wayfair.com');
  $: locations = isUSStore ? states : provinces;
  $: locationLabel = isUSStore ? 'State' : 'Province';

  $: if (show && initialUrl) {
    url = initialUrl;
  }

  function validateInput(): boolean {
    if (!url || !price || !productName || !location || !$user?.email) return false;
    
    const validationMessage = getValidationMessage(url, true);
    if (validationMessage) {
      error = validationMessage;
      return false;
    }

    const priceValue = parseFloat(price);
    if (isNaN(priceValue) || priceValue <= 0) {
      error = 'Please enter a valid price';
      return false;
    }

    return true;
  }

  async function handleSubmit() {
    if (!validateInput()) return;
    
    try {
      loading = true;
      error = '';
      
      loadingMessage = 'Fetching product data...';
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      loadingMessage = 'Saving product information...';
      
      const response = await fetchWithAuth('fetch-url', {
        method: 'POST',
        body: JSON.stringify({
          url,
          email: $user.email,
          productName,
          price: parseFloat(price),
          province: location
        })
      });

      if (!response.ok) {
        throw new Error('Failed to add product. There is a limit of 5 products per user.');
      }

      const data = await response.json();
      handleClose();
      
      dispatch('success');
      lastAddedProductId.set(data.id);
      
      setTimeout(() => {
        showCelebration = true;
        setTimeout(() => lastAddedProductId.set(null), 2000);
      }, 500);
      
    } catch (e) {
      error = e.message;
    } finally {
      loading = false;
      loadingMessage = 'Fetching product data...';
    }
  }

  function handleClose() {
    url = initialUrl;
    productName = '';
    price = '';
    location = '';
    error = '';
    dispatch('close');
  }

  function handleCelebrationClose() {
    showCelebration = false;
  }
</script>

{#if show}
  <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg p-6 w-full max-w-md relative">
      <CloseButton on:click={handleClose} />
      <h2 class="text-2xl font-bold mb-4">Add New Product</h2>
      
      <form on:submit|preventDefault={handleSubmit} class="space-y-4">
        <div>
          <label for="productName" class="block text-sm font-medium text-gray-700">Product Name</label>
          <input
            type="text"
            id="productName"
            bind:value={productName}
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary"
            required
            placeholder="Enter product name"
          />
        </div>

        <div>
          <label for="url" class="block text-sm font-medium text-gray-700">Product URL</label>
          <input
            type="url"
            id="url"
            bind:value={url}
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary"
            required
            placeholder={isUSStore ? "https://www.costco.com/product-url" : "https://www.costco.ca/product-url"}
          />
        </div>
        
        <div>
          <div class="flex items-center gap-2 mb-1">
            <label for="price" class="block text-sm font-medium text-gray-700">
              Desired Price ({isUSStore ? 'USD' : 'CAD'})
            </label>
            <div class="relative group">
              <HelpCircle class="w-4 h-4 text-gray-400 cursor-help" />
              <div class="invisible group-hover:visible absolute left-1/2 -translate-x-1/2 bottom-full mb-2 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg z-50">
                Enter the price you either paid for the item or the price you want to pay. If the price drops below this amount, we'll notify you immediately.
                <div class="absolute left-1/2 -translate-x-1/2 top-full w-2 h-2 bg-gray-800 transform rotate-45"></div>
              </div>
            </div>
          </div>
          <input
            type="number"
            id="price"
            bind:value={price}
            step="0.01"
            min="0"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary"
            required
            placeholder="99.99"
          />
        </div>

        <div>
          <label for="location" class="block text-sm font-medium text-gray-700">{locationLabel}</label>
          <select
            id="location"
            bind:value={location}
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary"
            required
          >
            <option value="">Select a {locationLabel.toLowerCase()}</option>
            {#each locations as { code, name }}
              <option value={code}>{name} ({code})</option>
            {/each}
          </select>
        </div>
        
        {#if error}
          <p class="text-red-500 text-sm">{error}</p>
        {/if}
        
        <div class="flex justify-end space-x-3">
          <Button variant="secondary" on:click={handleClose}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? loadingMessage : 'Add Product'}
          </Button>
        </div>
      </form>
    </div>
  </div>
{/if}

<CelebrationModal
  show={showCelebration}
  on:close={handleCelebrationClose}
/>