<script lang="ts">
  import { page } from '../stores/navigation';
  import { user, signOut } from '../stores/auth';
  import {
    LayoutDashboard,
    TrendingUp,
    Rss,
    FileText,
    HelpCircle,
    Info,
    LogOut,
    Menu,
    X,
    BookOpen,
    Coffee,
    Users
  } from 'lucide-svelte';

  let isMobileMenuOpen = false;

  function toggleMobileMenu() {
    isMobileMenuOpen = !isMobileMenuOpen;
  }

  function closeMobileMenu() {
    isMobileMenuOpen = false;
  }

  const navigationItems = [
    { 
      id: 'dashboard', 
      label: 'Dashboard', 
      icon: LayoutDashboard,
      requiresAuth: true 
    },
    { 
      id: 'feed', 
      label: 'Feed', 
      icon: Rss,
      requiresAuth: false 
    },
    { 
      id: 'price-history', 
      label: 'Price History', 
      icon: TrendingUp,
      requiresAuth: true 
    },
    {
      id: 'flyers',
      label: 'Flyers',
      icon: FileText,
      requiresAuth: false
    },
    {
      id: 'community',
      label: 'Community',
      icon: Users,
      requiresAuth: false
    },
    {
      id: 'blog',
      label: 'Blog',
      icon: BookOpen,
      requiresAuth: false
    },
    {
      id: 'faq',
      label: 'FAQ',
      icon: HelpCircle,
      requiresAuth: false
    },
    {
      id: 'about',
      label: 'About',
      icon: Info,
      requiresAuth: false
    }
  ];

  function handleNavigation(pageId: string) {
    page.set(pageId);
    closeMobileMenu();
  }

  function handleSignOut() {
    signOut();
    closeMobileMenu();
  }

  function getInitials(email: string): string {
    return email.charAt(0).toUpperCase();
  }
</script>

<!-- Mobile menu button -->
<div class="lg:hidden fixed top-4 left-4 z-50">
  <button
    class="p-2 bg-dark border border-gray-800/50 rounded-lg text-white hover:bg-dark-lighter transition-colors"
    on:click={toggleMobileMenu}
    aria-label="Toggle menu"
  >
    <Menu class="h-6 w-6" />
  </button>
</div>

<!-- Mobile overlay -->
{#if isMobileMenuOpen}
  <div
    class="lg:hidden fixed inset-0 bg-black/50 z-40"
    on:click={closeMobileMenu}
    role="button"
    tabindex="0"
    on:keydown={(e) => e.key === 'Escape' && closeMobileMenu()}
  ></div>
{/if}

<!-- Left Sidebar for authenticated users -->
<aside class="fixed left-0 top-0 h-full w-64 bg-dark border-r border-gray-800/50 backdrop-blur-xl z-50
             transform transition-transform duration-300 ease-in-out
             {isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}"
>
  <div class="flex flex-col h-full">
    <!-- Logo and Brand -->
    <div class="flex items-center justify-between px-6 py-4 border-b border-gray-800/50">
      <button
        class="flex items-center hover:opacity-80 transition-opacity"
        on:click={() => handleNavigation('home')}
      >
        <img src="/BargainHawk.svg" alt="BargainHawk" class="h-8 w-8 mr-3" />
        <span class="text-xl font-bold text-white">BargainHawk</span>
      </button>
      <!-- Close button for mobile -->
      <button
        class="lg:hidden p-1 text-gray-400 hover:text-white"
        on:click={closeMobileMenu}
        aria-label="Close menu"
      >
        <X class="h-6 w-6" />
      </button>
    </div>

    <!-- User Info -->
    {#if $user?.email}
      <div class="px-6 py-4 border-b border-gray-800/50">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
            <span class="text-white font-medium text-sm">
              {getInitials($user.email)}
            </span>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-white truncate">
              {$user.email}
            </p>
            <p class="text-xs text-gray-400">
              Signed in
            </p>
          </div>
        </div>
      </div>
    {/if}

    <!-- Navigation -->
    <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
      {#each navigationItems as item}
        {#if !item.requiresAuth || $user}
          <button
            class="w-full flex items-center px-4 py-3 text-left rounded-lg transition-all duration-200 group
                   {$page === item.id 
                     ? 'bg-primary/20 text-primary border border-primary/30' 
                     : 'text-gray-300 hover:text-white hover:bg-dark-lighter'}"
            on:click={() => handleNavigation(item.id)}
          >
            <svelte:component 
              this={item.icon} 
              class="h-5 w-5 mr-3 {$page === item.id ? 'text-primary' : 'text-gray-400 group-hover:text-white'}" 
            />
            <span class="font-medium">{item.label}</span>
          </button>
        {/if}
      {/each}
    </nav>

    <!-- Buy me a coffee & Sign Out -->
    <div class="px-4 py-4 border-t border-gray-800/50 space-y-2">
      <!-- Buy me a coffee button -->
      <a
        href="https://www.buymeacoffee.com/bargainhawk"
        target="_blank"
        rel="noopener noreferrer"
        class="w-full flex items-center px-4 py-3 text-left rounded-lg transition-all duration-200
               bg-[#FFDD00] hover:bg-[#FFDD00]/90 text-[#000000] group"
        aria-label="Buy me a coffee"
      >
        <Coffee class="h-5 w-5 mr-3 text-[#000000]" />
        <span class="font-medium">Buy me a coffee</span>
      </a>

      <!-- Sign Out button -->
      <button
        class="w-full flex items-center px-4 py-3 text-left rounded-lg transition-all duration-200
               text-gray-300 hover:text-white hover:bg-red-900/20 hover:border-red-500/30 group"
        on:click={handleSignOut}
      >
        <LogOut class="h-5 w-5 mr-3 text-gray-400 group-hover:text-red-400" />
        <span class="font-medium">Sign Out</span>
      </button>
    </div>
  </div>
</aside>

<!-- Spacer to prevent content from going under fixed sidebar on desktop -->
<div class="hidden lg:block w-64"></div>
