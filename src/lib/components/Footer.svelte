<script lang="ts">
  import { page } from '../stores/navigation';
</script>

<footer class="bg-gray-900 text-gray-400">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="flex flex-col md:flex-row justify-between space-y-8 md:space-y-0">
      <!-- Left Column -->
      <div class="md:w-1/3">
        <h3 class="text-lg text-white font-semibold mb-4">BargainHawk</h3>
        <p class="text-sm max-w-xs">
          Track Costco prices automatically and never miss a price adjustment opportunity.
        </p>
      </div>

      <!-- Middle Column -->
      <div class="md:w-1/3 md:flex md:justify-center">
        <div>
          <h3 class="text-lg text-white font-semibold mb-4">Quick Links</h3>
          <ul class="space-y-2 text-sm">
            <li>
              <button
                class="hover:text-white transition-colors w-full text-left"
                on:click={() => page.set('about')}
              >
                About
              </button>
            </li>
            <li>
              <button
                class="hover:text-white transition-colors w-full text-left"
                on:click={() => page.set('faq')}
              >
                FAQ
              </button>
            </li>
            <li>
              <button
                class="hover:text-white transition-colors w-full text-left"
                on:click={() => page.set('blog')}
              >
                Blog
              </button>
            </li>
            <li>
              <button
                class="hover:text-white transition-colors w-full text-left"
                on:click={() => page.set('privacy')}
              >
                Privacy Policy
              </button>
            </li>
            <li>
              <button
                class="hover:text-white transition-colors w-full text-left"
                on:click={() => page.set('terms')}
              >
                Terms of Service
              </button>
            </li>
          </ul>
        </div>
      </div>

      <!-- Right Column -->
      <div class="md:w-1/3 flex md:justify-end">
        <div>
          <h3 class="text-lg text-white font-semibold mb-4">Contact</h3>
          <p class="text-sm">
            Questions or feedback?<br />
            <a
              href="mailto:<EMAIL>"
              class="text-primary hover:text-secondary transition-colors"
            >
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>

    <!-- Copyright -->
    <div class="mt-12 pt-8 border-t border-gray-800 text-center text-sm">
      <p>
        &copy; {new Date().getFullYear()} 
        <a 
          href="https://maplecan.ca" 
          target="_blank" 
          rel="noopener noreferrer"
          class="text-primary hover:text-secondary transition-colors"
        >
          MapleCan Technologies Inc.
        </a> 
        All rights reserved.
      </p>
    </div>
  </div>
</footer>