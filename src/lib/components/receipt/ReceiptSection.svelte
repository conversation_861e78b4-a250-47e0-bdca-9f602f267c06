<script lang="ts">
    import { onMount } from 'svelte';
    import type { UploadMetadata } from '../../types/receipt';
    import ReceiptCard from './ReceiptCard.svelte';
    import ReceiptModal from './ReceiptModal.svelte';
    import { getUserReceipts } from '../../services/receipts';
    import { CircleAlert as AlertCircle } from 'lucide-svelte';
    
    export let refreshTrigger = 0; // Add this prop to trigger refreshes
    
    let receipts: UploadMetadata[] = [];
    let loading = true;
    let error = '';
    let selectedReceipt: UploadMetadata | null = null;
    let showModal = false;
  
    async function loadReceipts() {
      try {
        loading = true;
        error = '';
        receipts = await getUserReceipts();
        console.log('Loaded receipts:', receipts.length);
      } catch (e) {
        console.error('Error loading receipts:', e);
        // Only show error for genuine connection/authentication issues
        if (e instanceof Error) {
          if (e.message.includes('not authenticated')) {
            error = 'Please log in to view your receipts';
          } else if (e.message.includes('Unable to connect')) {
            error = 'Unable to connect to server. Please try again.';
          } else {
            error = 'Failed to load receipts. Please try again.';
          }
        } else {
          error = 'Failed to load receipts. Please try again.';
        }
        receipts = []; // Ensure receipts is empty array on error
      } finally {
        loading = false;
      }
    }
  
    function handleReceiptClick(event: CustomEvent<UploadMetadata>) {
      selectedReceipt = event.detail;
      showModal = true;
    }
  
    // Watch for refreshTrigger changes
    $: if (refreshTrigger) {
      loadReceipts();
    }
  
    onMount(loadReceipts);
  </script>
  
  <section class="mt-12">
    <h2 class="text-2xl font-bold text-white mb-6">Your Receipts</h2>
  
    {#if error}
      <div class="bg-red-900/20 border-l-4 border-red-500 p-4 mb-8 rounded-r-lg backdrop-blur-sm">
        <div class="flex items-center">
          <AlertCircle class="h-5 w-5 text-red-400 mr-2" />
          <p class="text-sm text-red-300">{error}</p>
        </div>
      </div>
    {/if}
  
    {#if loading}
      <div class="flex justify-center items-center min-h-[100px]">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    {:else if receipts.length === 0}
      <div class="text-center py-12 bg-dark-lighter/50 rounded-lg border border-gray-800/50 backdrop-blur-sm">
        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-800/50 flex items-center justify-center">
          <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
        </div>
        <h3 class="text-white font-medium mb-2">No receipts yet</h3>
        <p class="text-gray-400 text-sm">Upload your first receipt to start tracking your purchases</p>
      </div>
    {:else}
      <div class="grid gap-4">
        {#each receipts as receipt (receipt.id)}
          <ReceiptCard {receipt} on:click={handleReceiptClick} />
        {/each}
      </div>
    {/if}
  </section>
  
  <ReceiptModal
    show={showModal}
    receipt={selectedReceipt}
    on:close={() => showModal = false}
  />