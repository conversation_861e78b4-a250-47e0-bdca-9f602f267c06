<script lang="ts">
    import { onMount } from 'svelte';
    import type { UploadMetadata } from '../../types/receipt';
    import ReceiptCard from './ReceiptCard.svelte';
    import ReceiptModal from './ReceiptModal.svelte';
    import { getUserReceipts } from '../../services/receipts';
    import { CircleAlert as AlertCircle } from 'lucide-svelte';
    
    export let refreshTrigger = 0; // Add this prop to trigger refreshes
    
    let receipts: UploadMetadata[] = [];
    let loading = true;
    let error = '';
    let selectedReceipt: UploadMetadata | null = null;
    let showModal = false;
  
    async function loadReceipts() {
      try {
        loading = true;
        error = '';
        receipts = await getUserReceipts();
      } catch (e) {
        error = e instanceof Error ? e.message : 'Failed to load receipts';
      } finally {
        loading = false;
      }
    }
  
    function handleReceiptClick(event: CustomEvent<UploadMetadata>) {
      selectedReceipt = event.detail;
      showModal = true;
    }
  
    // Watch for refreshTrigger changes
    $: if (refreshTrigger) {
      loadReceipts();
    }
  
    onMount(loadReceipts);
  </script>
  
  <section class="mt-12">
    <h2 class="text-2xl font-bold text-white mb-6">Your Receipts</h2>
  
    {#if error}
      <div class="bg-red-900/20 border-l-4 border-red-500 p-4 mb-8 rounded-r-lg backdrop-blur-sm">
        <div class="flex items-center">
          <AlertCircle class="h-5 w-5 text-red-400 mr-2" />
          <p class="text-sm text-red-300">{error}</p>
        </div>
      </div>
    {/if}
  
    {#if loading}
      <div class="flex justify-center items-center min-h-[100px]">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    {:else if receipts.length === 0}
      <div class="text-center py-8 bg-dark-lighter/50 rounded-lg border border-gray-800/50 backdrop-blur-sm">
        <p class="text-gray-400">No receipts uploaded yet</p>
      </div>
    {:else}
      <div class="grid gap-4">
        {#each receipts as receipt (receipt.id)}
          <ReceiptCard {receipt} on:click={handleReceiptClick} />
        {/each}
      </div>
    {/if}
  </section>
  
  <ReceiptModal
    show={showModal}
    receipt={selectedReceipt}
    on:close={() => showModal = false}
  />