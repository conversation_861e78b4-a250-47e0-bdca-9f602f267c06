<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { User, AlertCircle } from 'lucide-svelte';
  import Button from '../ui/Button.svelte';
  import { createUserProfile } from '../../api/community';

  export let show = false;

  const dispatch = createEventDispatcher();

  let username = '';
  let firstName = '';
  let lastName = '';
  let loading = false;
  let error = '';

  async function handleSubmit() {
    if (!username.trim()) {
      error = 'Username is required';
      return;
    }

    if (username.length < 3 || username.length > 36) {
      error = 'Username must be between 3 and 36 characters';
      return;
    }

    loading = true;
    error = '';

    try {
      await createUserProfile({
        username: username.trim(),
        firstName: firstName.trim() || undefined,
        lastName: lastName.trim() || undefined
      });

      dispatch('success');
      show = false;
    } catch (err) {
      error = err.message || 'Failed to create profile';
    } finally {
      loading = false;
    }
  }

  function handleClose() {
    if (!loading) {
      show = false;
      dispatch('close');
    }
  }

  // Reset form when modal opens
  $: if (show) {
    username = '';
    firstName = '';
    lastName = '';
    error = '';
  }
</script>

{#if show}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-dark-light rounded-lg p-6 w-full max-w-md">
      <div class="flex items-center mb-4">
        <User class="h-6 w-6 text-primary mr-2" />
        <h2 class="text-xl font-bold text-white">Welcome to the Community!</h2>
      </div>
      
      <p class="text-gray-300 mb-6">
        To participate in the community, please choose a username. This will be displayed with your posts and comments.
      </p>

      <form on:submit|preventDefault={handleSubmit} class="space-y-4">
        <div>
          <label for="username" class="block text-sm font-medium text-gray-300 mb-1">
            Username *
          </label>
          <input
            id="username"
            type="text"
            bind:value={username}
            placeholder="Enter your username"
            maxlength="36"
            class="w-full px-3 py-2 bg-dark border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            disabled={loading}
            required
          />
          <p class="text-xs text-gray-400 mt-1">3-36 characters</p>
        </div>

        <div>
          <label for="firstName" class="block text-sm font-medium text-gray-300 mb-1">
            First Name (optional)
          </label>
          <input
            id="firstName"
            type="text"
            bind:value={firstName}
            placeholder="Enter your first name"
            maxlength="50"
            class="w-full px-3 py-2 bg-dark border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            disabled={loading}
          />
        </div>

        <div>
          <label for="lastName" class="block text-sm font-medium text-gray-300 mb-1">
            Last Name (optional)
          </label>
          <input
            id="lastName"
            type="text"
            bind:value={lastName}
            placeholder="Enter your last name"
            maxlength="50"
            class="w-full px-3 py-2 bg-dark border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            disabled={loading}
          />
        </div>

        {#if error}
          <div class="flex items-center text-red-400 text-sm">
            <AlertCircle class="h-4 w-4 mr-1" />
            {error}
          </div>
        {/if}

        <div class="flex gap-3 pt-4">
          <Button
            type="button"
            variant="secondary"
            on:click={handleClose}
            disabled={loading}
            class="flex-1"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading || !username.trim()}
            class="flex-1"
          >
            {loading ? 'Creating...' : 'Create Profile'}
          </Button>
        </div>
      </form>
    </div>
  </div>
{/if}
