<script lang="ts">
  import type { BlogPost } from '../../data/blogPosts';
  import { formatDate } from '../../utils/format';
  import { page } from '../../stores/navigation';
  import { renderMarkdown } from '../../utils/markdown';
  
  export let post: BlogPost;
  
  $: renderedContent = renderMarkdown(post.content);
  $: schemaString = JSON.stringify({
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://bargainhawk.ca/blog/${post.slug}`
    },
    "headline": post.title,
    "description": post.description,
    "image": {
      "@type": "ImageObject",
      "url": "https://bargainhawk.ca/logo.png",
      "width": 512,
      "height": 512
    },
    "keywords": post.keywords.join(', '),
    "datePublished": post.date,
    "dateModified": post.date,
    "author": {
      "@type": "Person",
      "name": post.author
    },
    "publisher": {
      "@type": "Organization",
      "name": "BargainHawk",
      "logo": {
        "@type": "ImageObject",
        "url": "https://bargainhawk.ca/logo.png",
        "width": 512,
        "height": 512
      }
    }
  });
</script>

<svelte:head>
  <title>{post.title} | BargainHawk Blog</title>
  <meta name="description" content={post.description} />
  <meta name="keywords" content={post.keywords.join(', ')} />
  <link rel="canonical" href="https://bargainhawk.ca/blog/{post.slug}" />
  
  <!-- Enhanced Meta Tags for Search Engines -->
  <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
  
  <!-- Open Graph Tags -->
  <meta property="og:title" content={post.title} />
  <meta property="og:description" content={post.description} />
  <meta property="og:type" content="article" />
  <meta property="og:url" content="https://bargainhawk.ca/blog/{post.slug}" />
  <meta property="og:site_name" content="BargainHawk" />
  <meta property="og:image" content="https://bargainhawk.ca/logo.png" />
  <meta property="article:published_time" content={post.date} />
  <meta property="article:modified_time" content={post.date} />
  <meta property="article:section" content="Shopping Tips" />
  <meta property="article:tag" content="Costco Price Adjustment" />
  
  <!-- Schema.org Markup -->
  <script type="application/ld+json">
    {schemaString}
  </script>
</svelte:head>

<article class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
  <header class="mb-8">
    <div class="flex items-center text-sm text-gray-500 mb-2">
      <time datetime={post.date}>{formatDate(post.date)}</time>
      <span class="mx-2">•</span>
      <span>{post.readingTime}</span>
    </div>
    <h1 class="text-4xl font-bold text-gray-900 mb-4">{post.title}</h1>
    <p class="text-xl text-gray-600">{post.description}</p>
  </header>
  
  <div class="prose prose-blue max-w-none">
    {@html renderedContent}
  </div>
</article>