<script lang="ts">
  import { faqs } from '../data/faqData';
  import FAQSection from '../components/faq/FAQSection.svelte';
  
  const generalFaqs = faqs.filter(faq => faq.category === 'general');
  const pricingFaqs = faqs.filter(faq => faq.category === 'pricing');
  const trackingFaqs = faqs.filter(faq => faq.category === 'tracking');
  const usageFaqs = faqs.filter(faq => faq.category === 'usage');
</script>

<svelte:head>
  <title>Costco Price Adjustment FAQ | BargainHawk</title>
  <meta name="description" content="Learn everything about Costco's price adjustment policy, including how to track items with multiple variations. Find out how to claim price adjustments and save money on your Costco purchases." />
  <meta name="keywords" content="costco price adjustment, costco price match, costco price protection, costco refund policy, costco price tracking" />
  <link rel="canonical" href="https://bargainhawk.ca/faq" />
  
  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I track items with multiple variations?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "For products with multiple variations (like different sizes or colors), always enter the lowest price from the available options. The product URL doesn't always reflect the specific variation, so this ensures accurate tracking."
          }
        },
        {
          "@type": "Question",
          "name": "Are you affiliated with Costco?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "No, BargainHawk is not affiliated with Costco in any way. We are an independent service created to help shoppers save money by tracking prices and facilitating price adjustment claims."
          }
        }
      ]
    }
  </script>
</svelte:head>

<section class="min-h-screen gradient-bg pt-20">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <h1 class="text-4xl font-bold text-white text-center mb-12">
      Frequently Asked Questions
    </h1>
    
    <div class="space-y-12">
      <FAQSection title="General Information" faqs={generalFaqs} />
      <FAQSection title="Price Tracking" faqs={trackingFaqs} />
      <FAQSection title="Price Adjustments" faqs={pricingFaqs} />
      <FAQSection title="Using BargainHawk" faqs={usageFaqs} />
    </div>
  </div>
</section>