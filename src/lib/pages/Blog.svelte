<script lang="ts">
    import { getAllBlogPosts, getBlogPost } from '../data/blogPosts';
    import BlogCard from '../components/blog/BlogCard.svelte';
    import BlogPost from '../components/blog/BlogPost.svelte';
    
    export let slug: string | undefined = undefined;
    
    $: post = slug ? getBlogPost(slug) : undefined;
    $: posts = getAllBlogPosts();
  </script>
  
  <svelte:head>
    {#if !slug}
      <title>Blog | BargainHawk - Costco Price Tracking Tips & Walmart Savings Guides</title>
      <meta name="description" content="Learn how to save money on your Costco purchases with our expert tips and guides. Discover Walmart price adjustments, price tracking strategies and maximize your savings." />
      <meta name="keywords" content="costco price tracking, costco savings tips, costco price adjustments, costco shopping guide, costco.ca tips, walmart price tracking, walmart price adjustments, walmart price drop alerts, walmart savings, walmart deals" />
      <link rel="canonical" href="https://bargainhawk.ca/blog" />
    {/if}
  </svelte:head>
  
  <div class="min-h-screen bg-gray-50 pt-20">
    {#if post}
      <BlogPost {post} />
    {:else}
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Latest Articles</h1>
        <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {#each posts as post}
            <BlogCard {post} />
          {/each}
        </div>
      </div>
    {/if}
  </div>