<script lang="ts">
  import { onMount } from 'svelte';
  import { user } from '../stores/auth';
  import { Heart, MessageCircle, Share2, TrendingUp, Clock, Users, Award, Plus, ChevronUp, ChevronDown } from 'lucide-svelte';
  import Button from '../components/ui/Button.svelte';
  import AuthModal from '../components/auth/AuthModal.svelte';
  import UsernameSetupModal from '../components/community/UsernameSetupModal.svelte';
  import {
    getPosts,
    createPost,
    upvotePost,
    downvotePost,
    checkUserProfileExists,
    type CommunityPost,
    type CreatePostRequest
  } from '../api/community';

  let showAuthModal = false;
  let showUsernameModal = false;
  let activeTab = 'hot';
  let showNewPostModal = false;
  let newPostTitle = '';
  let newPostContent = '';
  let newPostStore = 'costco';
  let newPostCategory = 'deals';
  let newPostProductUrl = '';
  let newPostProductName = '';
  let newPostOriginalPrice = '';
  let newPostCurrentPrice = '';
  let newPostSavings = '';

  let communityPosts: CommunityPost[] = [];
  let loading = true;
  let error = '';
  let hasUserProfile = false;

  onMount(async () => {
    await loadPosts();
    if ($user) {
      hasUserProfile = await checkUserProfileExists();
    }
  });

  async function loadPosts() {
    try {
      loading = true;
      error = '';
      const response = await getPosts({
        sort: activeTab === 'hot' ? 'hot' : 'new',
        size: 20
      });
      communityPosts = response.content;
    } catch (err) {
      error = 'Failed to load posts';
      console.error('Error loading posts:', err);
    } finally {
      loading = false;
    }
  }

  async function handleVote(postId: string, isUpvote: boolean) {
    if (!$user) {
      showAuthModal = true;
      return;
    }

    if (!hasUserProfile) {
      showUsernameModal = true;
      return;
    }

    try {
      const updatedPost = isUpvote ? await upvotePost(postId) : await downvotePost(postId);

      // Update the post in the local array
      communityPosts = communityPosts.map(post =>
        post.id === postId ? updatedPost : post
      );
    } catch (err) {
      console.error('Error voting on post:', err);
      // Could show a toast notification here
    }
  }

  async function handleCreatePost() {
    if (!$user) {
      showAuthModal = true;
      return;
    }

    if (!hasUserProfile) {
      showUsernameModal = true;
      return;
    }

    if (!newPostTitle.trim() || !newPostContent.trim()) {
      return;
    }

    try {
      const postData: CreatePostRequest = {
        title: newPostTitle.trim(),
        content: newPostContent.trim(),
        store: newPostStore,
        category: newPostCategory,
        productUrl: newPostProductUrl.trim() || undefined,
        productName: newPostProductName.trim() || undefined,
        originalPrice: newPostOriginalPrice ? parseFloat(newPostOriginalPrice) : undefined,
        currentPrice: newPostCurrentPrice ? parseFloat(newPostCurrentPrice) : undefined
      };

      const newPost = await createPost(postData);

      // Add the new post to the beginning of the array
      communityPosts = [newPost, ...communityPosts];

      // Reset form
      resetNewPostForm();
    } catch (err) {
      console.error('Error creating post:', err);
      // Could show error message to user
    }
  }

  function resetNewPostForm() {
    newPostTitle = '';
    newPostContent = '';
    newPostStore = 'costco';
    newPostCategory = 'deals';
    newPostProductUrl = '';
    newPostProductName = '';
    newPostOriginalPrice = '';
    newPostCurrentPrice = '';
    showNewPostModal = false;
  }

  function handleShare(post: CommunityPost) {
    const url = `${window.location.origin}/community/posts/${post.id}`;
    const text = `Check out this deal: ${post.title}`;

    if (navigator.share) {
      navigator.share({ title: post.title, text, url });
    } else {
      navigator.clipboard.writeText(url);
      // Could show a toast notification here
    }
  }

  function handleUsernameCreated() {
    hasUserProfile = true;
    showUsernameModal = false;
  }

  function handleTabChange(tab: string) {
    activeTab = tab;
    loadPosts();
  }

  function requireAuth(action: () => void) {
    console.log('requireAuth called, user:', $user, 'hasUserProfile:', hasUserProfile);
    if (!$user) {
      console.log('No user, showing auth modal');
      showAuthModal = true;
      return;
    }

    if (!hasUserProfile) {
      console.log('No user profile, showing username modal');
      showUsernameModal = true;
      console.log('showUsernameModal set to:', showUsernameModal);
      return;
    }

    console.log('User authenticated, executing action');
    action();
  }

  function getStoreColor(store: string) {
    const colors = {
      costco: 'bg-red-500',
      walmart: 'bg-blue-500',
      wayfair: 'bg-purple-500'
    };
    return colors[store] || 'bg-gray-500';
  }

  function formatTimeAgo(dateString: string) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  }

  function calculateSavings(originalPrice?: number, currentPrice?: number): string {
    if (!originalPrice || !currentPrice) return '';
    const savings = originalPrice - currentPrice;
    return savings > 0 ? `$${savings.toFixed(2)}` : '';
  }

  function handleNewPost() {
    console.log('handleNewPost clicked, user:', $user, 'hasUserProfile:', hasUserProfile);
    requireAuth(() => {
      console.log('Setting showNewPostModal to true');
      showNewPostModal = true;
    });
  }

  async function submitNewPost() {
    if (!newPostTitle.trim() || !newPostContent.trim()) {
      return;
    }

    try {
      const postData: CreatePostRequest = {
        title: newPostTitle,
        content: newPostContent,
        store: newPostStore,
        category: newPostCategory,
        productUrl: newPostProductUrl || undefined,
        productName: newPostProductName || undefined,
        originalPrice: newPostOriginalPrice ? parseFloat(newPostOriginalPrice) : undefined,
        currentPrice: newPostCurrentPrice ? parseFloat(newPostCurrentPrice) : undefined
      };

      await createPost(postData);
      await loadPosts(); // Refresh the posts

      // Reset form
      newPostTitle = '';
      newPostContent = '';
      newPostStore = 'costco';
      newPostCategory = 'deals';
      newPostProductUrl = '';
      newPostProductName = '';
      newPostOriginalPrice = '';
      newPostCurrentPrice = '';
      newPostSavings = '';
      showNewPostModal = false;
    } catch (err) {
      console.error('Error creating post:', err);
    }
  }

  $: filteredPosts = activeTab === 'hot'
    ? communityPosts.filter(post => post.isFeatured || post.upvotes > 10)
    : communityPosts;

  // Debug reactive statement
  $: console.log('showNewPostModal changed:', showNewPostModal);
</script>

<svelte:head>
  <title>Community Deals & Savings Tips | BargainHawk</title>
  <meta name="description" content="Join the BargainHawk community! Share deals, savings tips, and success stories. Connect with fellow bargain hunters and never miss a great deal." />
  <meta name="keywords" content="deals community, savings forum, bargain hunters, deal sharing, price drop community, costco deals, walmart deals, wayfair deals, canadian deals forum" />
</svelte:head>

<div class="min-h-screen bg-dark text-white">
  <!-- Header -->
  <div class="bg-gradient-to-r from-primary to-primary-dark py-12">
    <div class="max-w-6xl mx-auto px-4">
      <div class="text-center">
        <h1 class="text-4xl font-bold mb-4">
          <Users class="inline-block mr-3 h-10 w-10" />
          BargainHawk Community
        </h1>
        <p class="text-xl text-primary-light mb-6">
          Share deals, celebrate savings, and help fellow bargain hunters save money
        </p>
        <div class="flex justify-center gap-4 text-sm">
          <div class="flex items-center">
            <TrendingUp class="h-4 w-4 mr-1" />
            <span>2,847 Active Members</span>
          </div>
          <div class="flex items-center">
            <Award class="h-4 w-4 mr-1" />
            <span>$127K+ Community Savings</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-6xl mx-auto px-4 py-8">
    <!-- Action Bar -->
    <div class="flex justify-between items-center mb-8">
      <div class="flex gap-2">
        <button
          class="px-4 py-2 rounded-lg transition-colors {activeTab === 'hot' ? 'bg-primary text-white' : 'bg-dark-lighter text-gray-300 hover:text-white'}"
          on:click={() => activeTab = 'hot'}
        >
          🔥 Hot Deals
        </button>
        <button
          class="px-4 py-2 rounded-lg transition-colors {activeTab === 'recent' ? 'bg-primary text-white' : 'bg-dark-lighter text-gray-300 hover:text-white'}"
          on:click={() => activeTab = 'recent'}
        >
          <Clock class="inline h-4 w-4 mr-1" />
          Recent
        </button>
      </div>
      
      <Button on:click={handleNewPost} customClass="bg-primary hover:bg-primary-dark">
        <Plus class="h-4 w-4 mr-2" />
        Share a Deal
      </Button>
    </div>

    <!-- Community Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div class="bg-dark-lighter rounded-lg p-6 text-center">
        <div class="text-3xl font-bold text-primary mb-2">47</div>
        <div class="text-gray-300">Deals Shared Today</div>
      </div>
      <div class="bg-dark-lighter rounded-lg p-6 text-center">
        <div class="text-3xl font-bold text-green-400 mb-2">$12,847</div>
        <div class="text-gray-300">Saved This Week</div>
      </div>
      <div class="bg-dark-lighter rounded-lg p-6 text-center">
        <div class="text-3xl font-bold text-blue-400 mb-2">156</div>
        <div class="text-gray-300">Active Discussions</div>
      </div>
    </div>

    <!-- Community Posts -->
    <div class="space-y-6">
      {#each filteredPosts as post}
        <div class="bg-dark-lighter rounded-lg p-6 hover:bg-dark-lighter/80 transition-colors">
          <!-- Post Header -->
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white font-bold">
                {post.authorUsername?.charAt(0).toUpperCase() || 'U'}
              </div>
              <div>
                <div class="font-medium">{post.authorUsername || 'Unknown User'}</div>
                <div class="text-sm text-gray-400 flex items-center gap-2">
                  <span class="px-2 py-1 rounded text-xs text-white {getStoreColor(post.store)}">
                    {post.store.toUpperCase()}
                  </span>
                  <span>{formatTimeAgo(post.createdAt)}</span>
                  {#if calculateSavings(post.originalPrice, post.currentPrice)}
                    <span class="text-green-400 font-medium">Saved {calculateSavings(post.originalPrice, post.currentPrice)}</span>
                  {/if}
                </div>
              </div>
            </div>
            {#if post.isFeatured || post.upvotes > 10}
              <span class="px-2 py-1 bg-red-500 text-white text-xs rounded-full">🔥 HOT</span>
            {/if}
          </div>

          <!-- Post Content -->
          <h3 class="text-xl font-semibold mb-3">{post.title}</h3>
          <p class="text-gray-300 mb-4 leading-relaxed">{post.content}</p>

          <!-- Post Actions -->
          <div class="flex items-center gap-6 pt-4 border-t border-gray-700">
            <button
              class="flex items-center gap-2 text-gray-400 hover:text-red-400 transition-colors"
              on:click={() => handleVote(post.id, true)}
            >
              <Heart class="h-4 w-4" />
              <span>{post.upvotes}</span>
            </button>
            <button class="flex items-center gap-2 text-gray-400 hover:text-blue-400 transition-colors">
              <MessageCircle class="h-4 w-4" />
              <span>{post.commentCount}</span>
            </button>
            <button class="flex items-center gap-2 text-gray-400 hover:text-green-400 transition-colors">
              <Share2 class="h-4 w-4" />
              <span>Share</span>
            </button>
          </div>
        </div>
      {/each}
    </div>

    <!-- Call to Action -->
    <div class="mt-12 text-center bg-gradient-to-r from-primary/20 to-primary-dark/20 rounded-lg p-8">
      <h2 class="text-2xl font-bold mb-4">Join the Savings Revolution!</h2>
      <p class="text-gray-300 mb-6">
        Connect with thousands of smart shoppers. Share your best deals and discover amazing savings opportunities.
      </p>
      {#if !$user}
        <Button on:click={() => showAuthModal = true} customClass="bg-primary hover:bg-primary-dark">
          Join the Community - It's Free!
        </Button>
      {/if}
    </div>
  </div>
</div>

<!-- New Post Modal -->
{#if showNewPostModal}
  <!-- Debug: Modal should be visible now -->
  <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
    <div class="bg-dark-lighter rounded-lg p-6 w-full max-w-md">
      <h3 class="text-xl font-bold mb-4">Share a Deal</h3>
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium mb-2">Deal Title</label>
          <input
            type="text"
            bind:value={newPostTitle}
            placeholder="e.g., Amazing Costco Deal - 50% OFF!"
            class="w-full px-3 py-2 bg-dark border border-gray-600 rounded-lg text-white"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2">Store</label>
          <select bind:value={newPostStore} class="w-full px-3 py-2 bg-dark border border-gray-600 rounded-lg text-white">
            <option value="costco">Costco</option>
            <option value="walmart">Walmart</option>
            <option value="wayfair">Wayfair</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2">Savings Amount (optional)</label>
          <input
            type="text"
            bind:value={newPostSavings}
            placeholder="e.g., $25.00"
            class="w-full px-3 py-2 bg-dark border border-gray-600 rounded-lg text-white"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2">Deal Details</label>
          <textarea
            bind:value={newPostContent}
            placeholder="Share the details of this amazing deal..."
            rows="4"
            maxlength="500"
            class="w-full px-3 py-2 bg-dark border border-gray-600 rounded-lg text-white resize-none"
          ></textarea>
          <p class="text-xs text-gray-400 mt-1">{newPostContent.length}/500 characters</p>
        </div>
      </div>
      
      <div class="flex gap-3 mt-6">
        <Button on:click={submitNewPost} customClass="flex-1 bg-primary hover:bg-primary-dark">
          Share Deal
        </Button>
        <Button on:click={() => showNewPostModal = false} variant="secondary" customClass="flex-1">
          Cancel
        </Button>
      </div>
    </div>
  </div>
{/if}

<!-- Username Setup Modal -->
<UsernameSetupModal
  show={showUsernameModal}
  on:close={() => showUsernameModal = false}
  on:success={handleUsernameCreated}
/>

<!-- Auth Modal -->
{#if showAuthModal}
  <AuthModal on:close={() => showAuthModal = false} />
{/if}
