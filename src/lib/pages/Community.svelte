<script lang="ts">
  import { onMount } from 'svelte';
  import { user } from '../stores/auth';
  import { Heart, MessageCircle, Share2, TrendingUp, Clock, Users, Award, Plus } from 'lucide-svelte';
  import Button from '../components/ui/Button.svelte';
  import AuthModal from '../components/auth/AuthModal.svelte';

  let showAuthModal = false;
  let activeTab = 'hot';
  let showNewPostModal = false;
  let newPostTitle = '';
  let newPostContent = '';
  let newPostStore = 'costco';
  let newPostSavings = '';

  // Mock data for community posts - in real app, this would come from API
  let communityPosts = [
    {
      id: 1,
      title: "🔥 Costco Kirkland Olive Oil - 40% OFF!",
      content: "Just spotted this amazing deal at Costco Mississauga. Usually $24.99, now $14.99. Stock up time!",
      author: "DealHunter2024",
      store: "costco",
      savings: "$10.00",
      upvotes: 47,
      comments: 12,
      timeAgo: "2 hours ago",
      isHot: true,
      tags: ["food", "pantry", "kirkland"]
    },
    {
      id: 2,
      title: "Walmart Price Match Success Story",
      content: "Successfully price matched a $200 air fryer from Canadian Tire at Walmart. Saved an extra $30 with their rollback. Here's how I did it...",
      author: "SavingsQueen",
      store: "walmart", 
      savings: "$30.00",
      upvotes: 23,
      comments: 8,
      timeAgo: "4 hours ago",
      isHot: false,
      tags: ["appliances", "price-match", "success-story"]
    },
    {
      id: 3,
      title: "Wayfair Flash Sale Alert - Furniture 60% OFF",
      content: "Wayfair just launched a surprise flash sale. Dining sets, sofas, and bedroom furniture all heavily discounted. Sale ends midnight!",
      author: "HomeDecorPro",
      store: "wayfair",
      savings: "$500+",
      upvotes: 89,
      comments: 34,
      timeAgo: "1 hour ago", 
      isHot: true,
      tags: ["furniture", "flash-sale", "home-decor"]
    }
  ];

  function handleUpvote(postId: number) {
    if (!$user) {
      showAuthModal = true;
      return;
    }
    
    communityPosts = communityPosts.map(post => 
      post.id === postId 
        ? { ...post, upvotes: post.upvotes + 1 }
        : post
    );
  }

  function handleNewPost() {
    if (!$user) {
      showAuthModal = true;
      return;
    }
    showNewPostModal = true;
  }

  function submitNewPost() {
    if (!newPostTitle || !newPostContent) return;
    
    const newPost = {
      id: Date.now(),
      title: newPostTitle,
      content: newPostContent,
      author: $user?.email?.split('@')[0] || 'Anonymous',
      store: newPostStore,
      savings: newPostSavings,
      upvotes: 0,
      comments: 0,
      timeAgo: 'Just now',
      isHot: false,
      tags: []
    };
    
    communityPosts = [newPost, ...communityPosts];
    
    // Reset form
    newPostTitle = '';
    newPostContent = '';
    newPostSavings = '';
    showNewPostModal = false;
  }

  function getStoreColor(store: string) {
    const colors = {
      costco: 'bg-red-500',
      walmart: 'bg-blue-500', 
      wayfair: 'bg-purple-500'
    };
    return colors[store] || 'bg-gray-500';
  }

  $: filteredPosts = activeTab === 'hot' 
    ? communityPosts.filter(post => post.isHot)
    : communityPosts;
</script>

<svelte:head>
  <title>Community Deals & Savings Tips | BargainHawk</title>
  <meta name="description" content="Join the BargainHawk community! Share deals, savings tips, and success stories. Connect with fellow bargain hunters and never miss a great deal." />
  <meta name="keywords" content="deals community, savings forum, bargain hunters, deal sharing, price drop community, costco deals, walmart deals, wayfair deals, canadian deals forum" />
</svelte:head>

<div class="min-h-screen bg-dark text-white">
  <!-- Header -->
  <div class="bg-gradient-to-r from-primary to-primary-dark py-12">
    <div class="max-w-6xl mx-auto px-4">
      <div class="text-center">
        <h1 class="text-4xl font-bold mb-4">
          <Users class="inline-block mr-3 h-10 w-10" />
          BargainHawk Community
        </h1>
        <p class="text-xl text-primary-light mb-6">
          Share deals, celebrate savings, and help fellow bargain hunters save money
        </p>
        <div class="flex justify-center gap-4 text-sm">
          <div class="flex items-center">
            <TrendingUp class="h-4 w-4 mr-1" />
            <span>2,847 Active Members</span>
          </div>
          <div class="flex items-center">
            <Award class="h-4 w-4 mr-1" />
            <span>$127K+ Community Savings</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-6xl mx-auto px-4 py-8">
    <!-- Action Bar -->
    <div class="flex justify-between items-center mb-8">
      <div class="flex gap-2">
        <button
          class="px-4 py-2 rounded-lg transition-colors {activeTab === 'hot' ? 'bg-primary text-white' : 'bg-dark-lighter text-gray-300 hover:text-white'}"
          on:click={() => activeTab = 'hot'}
        >
          🔥 Hot Deals
        </button>
        <button
          class="px-4 py-2 rounded-lg transition-colors {activeTab === 'recent' ? 'bg-primary text-white' : 'bg-dark-lighter text-gray-300 hover:text-white'}"
          on:click={() => activeTab = 'recent'}
        >
          <Clock class="inline h-4 w-4 mr-1" />
          Recent
        </button>
      </div>
      
      <Button on:click={handleNewPost} class="bg-primary hover:bg-primary-dark">
        <Plus class="h-4 w-4 mr-2" />
        Share a Deal
      </Button>
    </div>

    <!-- Community Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div class="bg-dark-lighter rounded-lg p-6 text-center">
        <div class="text-3xl font-bold text-primary mb-2">47</div>
        <div class="text-gray-300">Deals Shared Today</div>
      </div>
      <div class="bg-dark-lighter rounded-lg p-6 text-center">
        <div class="text-3xl font-bold text-green-400 mb-2">$12,847</div>
        <div class="text-gray-300">Saved This Week</div>
      </div>
      <div class="bg-dark-lighter rounded-lg p-6 text-center">
        <div class="text-3xl font-bold text-blue-400 mb-2">156</div>
        <div class="text-gray-300">Active Discussions</div>
      </div>
    </div>

    <!-- Community Posts -->
    <div class="space-y-6">
      {#each filteredPosts as post}
        <div class="bg-dark-lighter rounded-lg p-6 hover:bg-dark-lighter/80 transition-colors">
          <!-- Post Header -->
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white font-bold">
                {post.author.charAt(0).toUpperCase()}
              </div>
              <div>
                <div class="font-medium">{post.author}</div>
                <div class="text-sm text-gray-400 flex items-center gap-2">
                  <span class="px-2 py-1 rounded text-xs text-white {getStoreColor(post.store)}">
                    {post.store.toUpperCase()}
                  </span>
                  <span>{post.timeAgo}</span>
                  {#if post.savings}
                    <span class="text-green-400 font-medium">Saved {post.savings}</span>
                  {/if}
                </div>
              </div>
            </div>
            {#if post.isHot}
              <span class="px-2 py-1 bg-red-500 text-white text-xs rounded-full">🔥 HOT</span>
            {/if}
          </div>

          <!-- Post Content -->
          <h3 class="text-xl font-semibold mb-3">{post.title}</h3>
          <p class="text-gray-300 mb-4 leading-relaxed">{post.content}</p>

          <!-- Post Actions -->
          <div class="flex items-center gap-6 pt-4 border-t border-gray-700">
            <button
              class="flex items-center gap-2 text-gray-400 hover:text-red-400 transition-colors"
              on:click={() => handleUpvote(post.id)}
            >
              <Heart class="h-4 w-4" />
              <span>{post.upvotes}</span>
            </button>
            <button class="flex items-center gap-2 text-gray-400 hover:text-blue-400 transition-colors">
              <MessageCircle class="h-4 w-4" />
              <span>{post.comments}</span>
            </button>
            <button class="flex items-center gap-2 text-gray-400 hover:text-green-400 transition-colors">
              <Share2 class="h-4 w-4" />
              <span>Share</span>
            </button>
          </div>
        </div>
      {/each}
    </div>

    <!-- Call to Action -->
    <div class="mt-12 text-center bg-gradient-to-r from-primary/20 to-primary-dark/20 rounded-lg p-8">
      <h2 class="text-2xl font-bold mb-4">Join the Savings Revolution!</h2>
      <p class="text-gray-300 mb-6">
        Connect with thousands of smart shoppers. Share your best deals and discover amazing savings opportunities.
      </p>
      {#if !$user}
        <Button on:click={() => showAuthModal = true} class="bg-primary hover:bg-primary-dark">
          Join the Community - It's Free!
        </Button>
      {/if}
    </div>
  </div>
</div>

<!-- New Post Modal -->
{#if showNewPostModal}
  <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
    <div class="bg-dark-lighter rounded-lg p-6 w-full max-w-md">
      <h3 class="text-xl font-bold mb-4">Share a Deal</h3>
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium mb-2">Deal Title</label>
          <input
            type="text"
            bind:value={newPostTitle}
            placeholder="e.g., Amazing Costco Deal - 50% OFF!"
            class="w-full px-3 py-2 bg-dark border border-gray-600 rounded-lg text-white"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2">Store</label>
          <select bind:value={newPostStore} class="w-full px-3 py-2 bg-dark border border-gray-600 rounded-lg text-white">
            <option value="costco">Costco</option>
            <option value="walmart">Walmart</option>
            <option value="wayfair">Wayfair</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2">Savings Amount (optional)</label>
          <input
            type="text"
            bind:value={newPostSavings}
            placeholder="e.g., $25.00"
            class="w-full px-3 py-2 bg-dark border border-gray-600 rounded-lg text-white"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2">Deal Details</label>
          <textarea
            bind:value={newPostContent}
            placeholder="Share the details of this amazing deal..."
            rows="4"
            class="w-full px-3 py-2 bg-dark border border-gray-600 rounded-lg text-white resize-none"
          ></textarea>
        </div>
      </div>
      
      <div class="flex gap-3 mt-6">
        <Button on:click={submitNewPost} class="flex-1 bg-primary hover:bg-primary-dark">
          Share Deal
        </Button>
        <Button on:click={() => showNewPostModal = false} variant="secondary" class="flex-1">
          Cancel
        </Button>
      </div>
    </div>
  </div>
{/if}

<!-- Auth Modal -->
{#if showAuthModal}
  <AuthModal on:close={() => showAuthModal = false} />
{/if}
