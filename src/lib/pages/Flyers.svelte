<script lang="ts">
    import { onMount } from 'svelte';
    import type { Flyer } from '../types/flyer';
    import FlyerViewer from '../components/flyer/FlyerViewer.svelte';
    import { getLatestFlyer } from '../services/flyers';
    
    let flyer: Flyer | null = null;
    let loading = true;
    let error = '';
  
    async function loadFlyer() {
      try {
        loading = true;
        error = '';
        flyer = await getLatestFlyer();
      } catch (e) {
        error = e instanceof Error ? e.message : 'Failed to load flyer';
      } finally {
        loading = false;
      }
    }
  
    onMount(loadFlyer);
  </script>
  
  <svelte:head>
    <title>Costco Canada Flyer | Current Weekly Deals and Sales | BargainHawk</title>
    <meta name="description" content="Browse the latest Costco Canada flyer. View current weekly deals, sales, and special offers. Find the best prices on groceries, electronics, home goods, and more at Costco." />
    <meta name="keywords" content="costco flyer, costco canada flyer, costco weekly flyer, costco deals, costco sales, costco weekly deals, costco weekly sales, costco weekly offers, costco current flyer, costco this week" />
    <link rel="canonical" href="https://bargainhawk.ca/flyers" />
    
    <!-- Open Graph Tags -->
    <meta property="og:title" content="Costco Canada Flyer | Current Weekly Deals and Sales" />
    <meta property="og:description" content="Browse the latest Costco Canada flyer. View current weekly deals, sales, and special offers. Find the best prices on groceries, electronics, home goods, and more at Costco." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://bargainhawk.ca/flyers" />
    <meta property="og:site_name" content="BargainHawk" />
    
    <!-- Schema.org Markup -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Costco Canada Flyer",
        "description": "Browse the latest Costco Canada flyer. View current weekly deals, sales, and special offers.",
        "publisher": {
          "@type": "Organization",
          "name": "BargainHawk",
          "url": "https://bargainhawk.ca"
        },
        "url": "https://bargainhawk.ca/flyers"
      }
    </script>
  </svelte:head>
  
  <div class="min-h-screen gradient-bg pt-24">
    {#if loading}
      <div class="flex justify-center items-center min-h-[200px]">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    {:else if error}
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-red-900/20 border-l-4 border-red-500 p-4 rounded-r-lg backdrop-blur-sm">
          <p class="text-red-400">{error}</p>
        </div>
      </div>
    {:else if flyer}
      <FlyerViewer {flyer} />
    {:else}
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
        <h2 class="text-2xl font-bold text-white mb-2">No Current Flyer</h2>
        <p class="text-gray-400">Check back later for new deals!</p>
      </div>
    {/if}
  </div>