<script lang="ts">
    import type { Flyer } from '../types/flyer';
    import FlyerViewer from '../components/flyer/FlyerViewer.svelte';
    import StoreSelector from '../components/flyer/StoreSelector.svelte';
    import { getLatestFlyer } from '../services/flyers';

    let selectedStore: string | null = null;
    let flyer: Flyer | null = null;
    let loading = false;
    let error = '';

    async function loadFlyer(vendorName: string) {
      try {
        loading = true;
        error = '';
        flyer = await getLatestFlyer(vendorName);
      } catch (e) {
        error = e instanceof Error ? e.message : 'Failed to load flyer';
        flyer = null;
      } finally {
        loading = false;
      }
    }

    function handleStoreSelect(event: CustomEvent<string>) {
      selectedStore = event.detail;
      loadFlyer(selectedStore);
    }

    function handleBackToStores() {
      selectedStore = null;
      flyer = null;
      error = '';
    }
  </script>
  
  <svelte:head>
    <title>Weekly Flyers | Current Store Deals and Sales | BargainHawk</title>
    <meta name="description" content="Browse the latest weekly flyers from your favorite stores. View current deals, sales, and special offers from Costco, Walmart, and more. Find the best prices on groceries, electronics, and home goods." />
    <meta name="keywords" content="weekly flyers, store flyers, costco flyer, walmart flyer, grocery flyers, weekly deals, store sales, weekly specials, current flyers, store circulars" />
    <link rel="canonical" href="https://bargainhawk.ca/flyers" />

    <!-- Open Graph Tags -->
    <meta property="og:title" content="Weekly Flyers | Current Store Deals and Sales" />
    <meta property="og:description" content="Browse the latest weekly flyers from your favorite stores. View current deals, sales, and special offers from Costco, Walmart, and more." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://bargainhawk.ca/flyers" />
    <meta property="og:site_name" content="BargainHawk" />

    <!-- Schema.org Markup -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Weekly Flyers",
        "description": "Browse the latest weekly flyers from your favorite stores. View current deals, sales, and special offers.",
        "publisher": {
          "@type": "Organization",
          "name": "BargainHawk",
          "url": "https://bargainhawk.ca"
        },
        "url": "https://bargainhawk.ca/flyers"
      }
    </script>
  </svelte:head>
  
  <div class="min-h-screen gradient-bg pt-24">
    {#if !selectedStore}
      <!-- Store Selection View -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center mb-12">
          <h1 class="text-4xl font-bold text-white mb-4">Weekly Flyers</h1>
          <p class="text-xl text-gray-400">Browse the latest deals and savings from your favorite stores</p>
        </div>
        <StoreSelector on:storeSelect={handleStoreSelect} />
      </div>
    {:else}
      <!-- Flyer View -->
      {#if loading}
        <div class="flex justify-center items-center min-h-[200px]">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      {:else if error}
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="mb-6">
            <button
              class="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
              on:click={handleBackToStores}
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
              Back to Stores
            </button>
          </div>
          <div class="bg-red-900/20 border-l-4 border-red-500 p-4 rounded-r-lg backdrop-blur-sm">
            <p class="text-red-400">{error}</p>
          </div>
        </div>
      {:else if flyer}
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
          <button
            class="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
            on:click={handleBackToStores}
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            Back to Stores
          </button>
        </div>
        <FlyerViewer {flyer} />
      {:else}
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
          <button
            class="flex items-center gap-2 text-gray-400 hover:text-white transition-colors mb-6 mx-auto"
            on:click={handleBackToStores}
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            Back to Stores
          </button>
          <h2 class="text-2xl font-bold text-white mb-2">No Current Flyer</h2>
          <p class="text-gray-400">Check back later for new deals!</p>
        </div>
      {/if}
    {/if}
  </div>