  <script lang="ts">
    import Hero from '../components/landing/Hero.svelte';
    import Features from '../components/landing/Features.svelte';
    import HowItWorks from '../components/landing/HowItWorks.svelte';
    import Testimonials from '../components/landing/Testimonials.svelte';
    import CTASection from '../components/landing/CTASection.svelte';
    import SupportedStores from '../components/SupportedStores.svelte';
    import AuthModal from '../components/AuthModal.svelte';
    
    let showAuthModal = false;
  </script>

  <svelte:head>
    <title>BargainHawk - Price Tracker for Costco, Wayfair & Walmart | Get Price Drop Alerts</title>
    <meta name="description" content="Track prices automatically on Costco, Wayfair, and Walmart. Get notified when prices drop and never miss a deal. Price tracking for Canada and USA stores." />
    <meta name="keywords" content="price tracking, costco price tracker, walmart price alerts, wayfair price monitoring, price drop notifications, deal alerts" />
  </svelte:head>

  <main>
    <Hero on:showAuth={() => showAuthModal = true} />
    <Features />
    <HowItWorks />
    <Testimonials />
    <SupportedStores />
    <CTASection on:signup={() => showAuthModal = true} />
  </main>

  <AuthModal
    show={showAuthModal}
    on:close={() => showAuthModal = false}
  />