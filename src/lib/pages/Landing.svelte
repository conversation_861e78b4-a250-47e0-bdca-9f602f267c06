  <script lang="ts">
    import Hero from '../components/landing/Hero.svelte';
    import Features from '../components/landing/Features.svelte';
    import HowItWorks from '../components/landing/HowItWorks.svelte';
    import Testimonials from '../components/landing/Testimonials.svelte';
    import CTASection from '../components/landing/CTASection.svelte';
    import SupportedStores from '../components/SupportedStores.svelte';
    import AuthModal from '../components/AuthModal.svelte';
    
    let showAuthModal = false;
  </script>

  <svelte:head>
    <title>BargainHawk - Costco Canada Price Adjustment Tracker | Get Price Drop Alerts</title>
    <meta name="description" content="Track Costco.ca prices automatically. Get notified when prices drop on Costco Canada and claim your price adjustment. Never miss a Costco.ca price drop again!" />
    <meta name="keywords" content="costco price tracking, costco price adjustment, costco price drop alerts, costco.ca price tracker, costco canada price monitoring, walmart price tracking, walmart price adjustment, walmart price drop alerts" />
  </svelte:head>

  <main>
    <Hero on:showAuth={() => showAuthModal = true} />
    <Features />
    <HowItWorks />
    <Testimonials />
    <SupportedStores />
    <CTASection on:signup={() => showAuthModal = true} />
  </main>

  <AuthModal
    show={showAuthModal}
    on:close={() => showAuthModal = false}
  />