<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '../stores/navigation';
  import { getAllBlogPosts, getBlogPost, getBlogPostsByCategory, blogCategories, type BlogMetadata, type BlogPost } from '../blogs/index';
  import BlogCard from '../components/blog/BlogCard.svelte';
  import BlogPostComponent from '../components/blog/BlogPost.svelte';
  import { renderMarkdown } from '../utils/markdown';
  
  let currentSlug: string | null = null;
  let currentPost: BlogPost | null = null;
  let allPosts: BlogMetadata[] = [];
  let selectedCategory: string = 'all';
  let filteredPosts: BlogMetadata[] = [];
  let loading = false;
  
  // Extract slug from URL path like /blogs/costco-price-adjustment-hack
  $: {
    const currentPage = $page;
    if (currentPage.startsWith('blogs/')) {
      currentSlug = currentPage.replace('blogs/', '');
    } else {
      currentSlug = null;
    }
  }
  
  // Load blog post content when slug changes
  $: if (currentSlug) {
    loadBlogPost(currentSlug);
  }
  
  // Filter posts by category
  $: {
    if (selectedCategory === 'all') {
      filteredPosts = allPosts;
    } else {
      filteredPosts = getBlogPostsByCategory(selectedCategory);
    }
  }
  
  async function loadBlogPost(slug: string) {
    loading = true;
    try {
      currentPost = await getBlogPost(slug);
    } catch (error) {
      console.error('Failed to load blog post:', error);
      currentPost = null;
    } finally {
      loading = false;
    }
  }
  
  onMount(() => {
    allPosts = getAllBlogPosts();
    filteredPosts = allPosts;
  });
</script>

<svelte:head>
  {#if currentPost}
    <title>{currentPost.title} | BargainHawk</title>
    <meta name="description" content={currentPost.description} />
    <meta name="keywords" content={currentPost.keywords.join(', ')} />
    <link rel="canonical" href={currentPost.canonicalUrl || `https://bargainhawk.ca/blogs/${currentPost.slug}`} />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article" />
    <meta property="og:title" content={currentPost.title} />
    <meta property="og:description" content={currentPost.description} />
    <meta property="og:url" content={`https://bargainhawk.ca/blogs/${currentPost.slug}`} />
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={currentPost.title} />
    <meta name="twitter:description" content={currentPost.description} />
    
    <!-- Article specific -->
    <meta property="article:published_time" content={currentPost.date} />
    <meta property="article:author" content={currentPost.author} />
    {#if currentPost.tags}
      {#each currentPost.tags as tag}
        <meta property="article:tag" content={tag} />
      {/each}
    {/if}
    
    <!-- Schema.org structured data -->
    <script type="application/ld+json">
      {JSON.stringify({
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "mainEntityOfPage": {
          "@type": "WebPage",
          "@id": `https://bargainhawk.ca/blogs/${currentPost.slug}`
        },
        "headline": currentPost.title,
        "description": currentPost.description,
        "datePublished": currentPost.date,
        "dateModified": currentPost.lastModified || currentPost.date,
        "author": {
          "@type": "Organization",
          "name": currentPost.author
        },
        "publisher": {
          "@type": "Organization",
          "name": "BargainHawk",
          "logo": {
            "@type": "ImageObject",
            "url": "https://bargainhawk.ca/logo.png"
          }
        },
        "keywords": currentPost.keywords.join(', ')
      })}
    </script>
  {:else}
    <title>Money-Saving Guides & Deal Tips | BargainHawk Blog</title>
    <meta name="description" content="Expert guides on price tracking, Costco price adjustments, Walmart deals, and money-saving strategies for Canadian shoppers. Save more with BargainHawk." />
    <meta name="keywords" content="money saving blog, price tracking guides, costco deals, walmart savings, canadian shopping tips" />
    <link rel="canonical" href="https://bargainhawk.ca/blogs" />
  {/if}
</svelte:head>

<div class="min-h-screen bg-gray-50 pt-20">
  {#if loading}
    <div class="flex justify-center items-center py-20">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
    </div>
  {:else if currentPost}
    <!-- Individual Blog Post View -->
    <article class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <header class="mb-8">
        <!-- Breadcrumb -->
        <nav class="mb-6">
          <ol class="flex items-center space-x-2 text-sm text-gray-500">
            <li><a href="/" class="hover:text-primary">Home</a></li>
            <li>/</li>
            <li><a href="/blogs" class="hover:text-primary">Blog</a></li>
            <li>/</li>
            <li class="text-gray-900">{currentPost.title}</li>
          </ol>
        </nav>
        
        <h1 class="text-4xl font-bold text-gray-900 mb-4">{currentPost.title}</h1>
        
        <div class="flex items-center space-x-4 text-sm text-gray-600 mb-6">
          <span>By {currentPost.author}</span>
          <span>•</span>
          <time datetime={currentPost.date}>{new Date(currentPost.date).toLocaleDateString('en-CA', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })}</time>
          <span>•</span>
          <span>{currentPost.readingTime}</span>
          {#if currentPost.category}
            <span>•</span>
            <span class="bg-primary/10 text-primary px-2 py-1 rounded-full text-xs font-medium">
              {blogCategories.find(cat => cat.id === currentPost.category)?.name || currentPost.category}
            </span>
          {/if}
        </div>
        
        <p class="text-xl text-gray-600">{currentPost.description}</p>
      </header>
      
      <div class="prose prose-blue max-w-none">
        {@html renderMarkdown(currentPost.content)}
      </div>
      
      <!-- Contextual links for SEO and user engagement -->
      <div class="mt-12 pt-8 border-t border-gray-200">
        <div class="bg-gray-50 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Ready to Put This Into Action?</h3>
          <p class="text-gray-600 mb-4">
            Start tracking prices and never miss a deal again with BargainHawk's free tools.
          </p>
          <div class="flex flex-wrap gap-3">
            <a href="/auth" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors text-sm font-medium">
              Get Started Free
            </a>
            <a href="/feed" class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors text-sm font-medium">
              View Price Drops
            </a>
            <a href="/community" class="text-primary hover:text-primary-dark underline text-sm font-medium">
              Join Community
            </a>
            <a href="/blogs" class="text-primary hover:text-primary-dark underline text-sm font-medium">
              More Articles
            </a>
          </div>
        </div>
        
        <!-- Related links based on post content -->
        <div class="mt-6">
          <h4 class="text-md font-medium text-gray-900 mb-3">Related Resources:</h4>
          <div class="flex flex-wrap gap-4 text-sm">
            {#if currentPost.slug.includes('costco')}
              <a href="/blogs/costco-price-adjustment-hack" class="text-primary hover:text-primary-dark underline">
                Costco Price Adjustment Guide
              </a>
              <a href="/blogs/how-to-access-costco-receipts" class="text-primary hover:text-primary-dark underline">
                Access Costco Receipts
              </a>
            {/if}
            {#if currentPost.slug.includes('walmart')}
              <a href="/blogs/walmart-price-adjustment-guide" class="text-primary hover:text-primary-dark underline">
                Walmart Price Adjustments
              </a>
            {/if}
            {#if currentPost.slug.includes('deals') || currentPost.slug.includes('savings')}
              <a href="/blogs/best-deals-today" class="text-primary hover:text-primary-dark underline">
                Best Deals Today
              </a>
              <a href="/blogs/money-saving-apps-canada" class="text-primary hover:text-primary-dark underline">
                Money Saving Apps
              </a>
            {/if}
            <a href="/faq" class="text-primary hover:text-primary-dark underline">
              FAQ
            </a>
            <a href="/about" class="text-primary hover:text-primary-dark underline">
              About BargainHawk
            </a>
          </div>
        </div>
      </div>
    </article>
  {:else}
    <!-- Blog List View -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="text-center mb-12">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Money-Saving Guides & Tips</h1>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Expert guides on price tracking, deal hunting, and money-saving strategies for Canadian shoppers. 
          <a href="/feed" class="text-primary hover:text-primary-dark underline">Browse current price drops</a> or 
          <a href="/community" class="text-primary hover:text-primary-dark underline">join our community</a> 
          to share your own money-saving discoveries.
        </p>
      </div>
      
      <!-- Category Filter -->
      <div class="flex flex-wrap justify-center gap-2 mb-8">
        <button
          class="px-4 py-2 rounded-full text-sm font-medium transition-colors {selectedCategory === 'all' ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}"
          on:click={() => selectedCategory = 'all'}
        >
          All Posts
        </button>
        {#each blogCategories as category}
          <button
            class="px-4 py-2 rounded-full text-sm font-medium transition-colors {selectedCategory === category.id ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}"
            on:click={() => selectedCategory = category.id}
          >
            {category.name}
          </button>
        {/each}
      </div>
      
      <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {#each filteredPosts as post}
          <BlogCard {post} urlPrefix="/blogs/" />
        {/each}
      </div>
      
      <!-- Additional contextual links for SEO -->
      <div class="mt-16 text-center bg-gray-100 rounded-lg p-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Ready to Start Saving?</h2>
        <p class="text-gray-600 mb-6">
          Put these tips into action with our free price tracking tools.
        </p>
        <div class="flex flex-wrap justify-center gap-4">
          <a href="/auth" class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-dark transition-colors">
            Get Started Free
          </a>
          <a href="/about" class="border border-primary text-primary px-6 py-3 rounded-lg hover:bg-primary hover:text-white transition-colors">
            Learn More
          </a>
        </div>
        <div class="mt-4">
          <a href="/faq" class="text-primary hover:text-primary-dark underline mr-4">
            Frequently Asked Questions
          </a>
          <a href="/flyers" class="text-primary hover:text-primary-dark underline">
            Browse Store Flyers
          </a>
        </div>
      </div>
    </div>
  {/if}
</div>
