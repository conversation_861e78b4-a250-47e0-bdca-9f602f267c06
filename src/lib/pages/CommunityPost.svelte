<script lang="ts">
  import { onMount } from 'svelte';
  import { user } from '../stores/auth';
  import { MessageCircle, Share2, ArrowLeft, ExternalLink, ChevronUp, ChevronDown } from 'lucide-svelte';
  import Button from '../components/ui/Button.svelte';
  import AuthModal from '../components/auth/AuthModal.svelte';
  import UsernameSetupModal from '../components/community/UsernameSetupModal.svelte';
  import {
    getPost,
    getComments,
    createComment,
    upvotePost,
    downvotePost,
    upvoteComment,
    downvoteComment,
    checkUserProfileExists,
    type CommunityPost,
    type CommunityComment
  } from '../api/community';

  let post: CommunityPost | null = null;
  let comments: CommunityComment[] = [];
  let loading = true;
  let error = '';
  let showAuthModal = false;
  let showUsernameModal = false;
  let hasUserProfile = false;
  let newComment = '';
  let submittingComment = false;
  let replyingTo: string | null = null;
  let replyContent = '';

  // Get post ID from URL path
  let postId = '';

  function getPostIdFromUrl() {
    const path = window.location.pathname;
    const match = path.match(/\/community\/([^\/]+)/);
    return match ? match[1] : '';
  }

  onMount(async () => {
    postId = getPostIdFromUrl();
    if ($user) {
      hasUserProfile = await checkUserProfileExists();
    }
    await loadPost();
    await loadComments();
  });

  async function loadPost() {
    if (!postId) return;
    
    try {
      loading = true;
      error = '';
      post = await getPost(postId);
      console.log('=== POST DEBUG INFO ===');
      console.log('Loaded post data:', post);
      console.log('Post productUrl:', post?.productUrl);
      console.log('Post productUrl type:', typeof post?.productUrl);
      console.log('Post productUrl length:', post?.productUrl?.length);
      console.log('Post productUrl truthy:', !!post?.productUrl);
      console.log('=== END POST DEBUG ===');
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load post';
      console.error('Error loading post:', err);
    } finally {
      loading = false;
    }
  }

  async function loadComments() {
    if (!postId) return;
    
    try {
      comments = await getComments(postId);
    } catch (err) {
      console.error('Error loading comments:', err);
    }
  }

  async function handleVote(isUpvote: boolean) {
    if (!post) return;

    requireAuth(async () => {
      try {
        const updatedPost = isUpvote ? await upvotePost(post!.id) : await downvotePost(post!.id);
        post = updatedPost;
      } catch (err) {
        console.error('Error voting on post:', err);
      }
    });
  }

  async function handleCommentVote(commentId: string, isUpvote: boolean) {
    requireAuth(async () => {
      try {
        const updatedComment = isUpvote ? await upvoteComment(commentId) : await downvoteComment(commentId);
        // Update the comment in the comments array
        comments = comments.map(c => c.id === commentId ? updatedComment : c);
      } catch (err) {
        console.error('Error voting on comment:', err);
      }
    });
  }

  async function submitComment() {
    if (!newComment.trim() || !post) return;

    requireAuth(async () => {
      try {
        submittingComment = true;
        await createComment(post!.id, { content: newComment.trim() });
        newComment = '';
        await loadComments(); // Reload comments to show the new one
      } catch (err) {
        console.error('Error creating comment:', err);
      } finally {
        submittingComment = false;
      }
    });
  }

  async function submitReply(parentCommentId: string) {
    if (!replyContent.trim() || !post) return;

    requireAuth(async () => {
      try {
        await createComment(post!.id, {
          content: replyContent.trim(),
          parentCommentId
        });
        replyContent = '';
        replyingTo = null;
        await loadComments(); // Reload comments to show the new reply
      } catch (err) {
        console.error('Error creating reply:', err);
      }
    });
  }

  function requireAuth(action: () => void) {
    if (!$user) {
      showAuthModal = true;
      return;
    }

    if (!hasUserProfile) {
      showUsernameModal = true;
      return;
    }

    action();
  }

  function handleUsernameCreated() {
    hasUserProfile = true;
    showUsernameModal = false;
  }

  function sharePost() {
    if (!post) return;

    const url = `${window.location.origin}/community/${post.id}`;

    navigator.clipboard.writeText(url).then(() => {
      alert('Link copied to clipboard! Share this deal with anyone you need.');
    }).catch(() => {
      // Fallback for older browsers
      alert(`Copy this link to share: ${url}`);
    });
  }

  function getStoreColor(store: string) {
    const colors = {
      costco: 'bg-red-500',
      walmart: 'bg-blue-500',
      wayfair: 'bg-purple-500'
    };
    return colors[store as keyof typeof colors] || 'bg-gray-500';
  }

  function formatTimeAgo(dateString: string) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  }

  function formatPrice(price: number) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  }

  function calculateSavings(original: number, current: number) {
    const savings = original - current;
    const percentage = Math.round((savings / original) * 100);
    return { amount: savings, percentage };
  }
</script>

<svelte:head>
  {#if post}
    <title>{post.title} - BargainHawk Community</title>
    <meta name="description" content={post.content.substring(0, 160)} />
    <meta property="og:title" content={post.title} />
    <meta property="og:description" content={post.content.substring(0, 160)} />
    <meta property="og:url" content="{window.location.origin}/community/{post.id}" />
    {#if post.productImage}
      <meta property="og:image" content={post.productImage} />
    {/if}
  {/if}
</svelte:head>

<div class="min-h-screen bg-dark text-white">
  <div class="container mx-auto px-4 py-8 pt-20 max-w-4xl">
    <!-- Back Button -->
    <div class="mb-6">
      <Button 
        on:click={() => window.history.back()} 
        variant="secondary" 
        customClass="flex items-center gap-2"
      >
        <ArrowLeft class="h-4 w-4" />
        Back to Community
      </Button>
    </div>

    {#if loading}
      <div class="flex justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    {:else if error}
      <div class="text-center py-12">
        <p class="text-red-400 mb-4">{error}</p>
        <Button on:click={loadPost}>Try Again</Button>
      </div>
    {:else if post}
      <!-- Post Content -->
      <div class="bg-dark-lighter rounded-lg p-6 mb-6">
        <!-- Post Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <span class="px-2 py-1 rounded text-xs font-medium text-white {getStoreColor(post.store)}">
              {post.store.toUpperCase()}
            </span>
            <span class="text-gray-400 text-sm">
              by {post.authorUsername} • {formatTimeAgo(post.createdAt)}
            </span>
          </div>
          <Button on:click={sharePost} variant="secondary" customClass="flex items-center gap-2">
            <Share2 class="h-4 w-4" />
            Share
          </Button>
        </div>

        <!-- Post Title -->
        <h1 class="text-2xl font-bold mb-4">{post.title}</h1>

        <!-- Product Info -->
        {#if post.productName || post.originalPrice || post.currentPrice || post.productUrl}
          <div class="bg-dark border border-gray-600 rounded-lg p-4 mb-4">
            {#if post.productName}
              <h3 class="font-semibold mb-2">{post.productName}</h3>
            {/if}
            
            {#if post.originalPrice && post.currentPrice}
              {@const savings = calculateSavings(post.originalPrice, post.currentPrice)}
              <div class="flex items-center gap-4 text-sm">
                <span class="text-gray-400 line-through">{formatPrice(post.originalPrice)}</span>
                <span class="text-green-400 font-bold text-lg">{formatPrice(post.currentPrice)}</span>
                <span class="bg-green-500 text-white px-2 py-1 rounded text-xs">
                  Save {formatPrice(savings.amount)} ({savings.percentage}% OFF)
                </span>
              </div>
            {/if}

            <!-- Debug: Product URL rendering -->
            {#if post.productUrl}
              <div class="bg-red-500 text-white p-2 mb-2">
                DEBUG: Product URL found: {post.productUrl}
              </div>
              <a
                href={post.productUrl}
                target="_blank"
                rel="noopener noreferrer"
                class="inline-flex items-center gap-1 text-primary hover:text-primary-light mt-2"
              >
                View Product <ExternalLink class="h-3 w-3" />
              </a>
            {:else}
              <div class="bg-yellow-500 text-black p-2 mb-2">
                DEBUG: No product URL found. Post productUrl: {post.productUrl}
              </div>
            {/if}
          </div>
        {/if}

        <!-- Post Content -->
        <div class="prose prose-invert max-w-none mb-4">
          <p class="whitespace-pre-wrap">{post.content}</p>
        </div>

        <!-- Post Actions -->
        <div class="flex items-center gap-4 pt-4 border-t border-gray-600">
          <div class="flex items-center gap-2">
            <Button
              on:click={() => handleVote(true)}
              variant="secondary"
              customClass="flex items-center gap-1 {post.hasUpvoted ? 'text-orange-400' : 'text-gray-400'}"
            >
              <ChevronUp class="h-4 w-4" />
              {post.upvotes}
            </Button>
            <Button
              on:click={() => handleVote(false)}
              variant="secondary"
              customClass="flex items-center gap-1 {post.hasDownvoted ? 'text-blue-400' : 'text-gray-400'}"
            >
              <ChevronDown class="h-4 w-4" />
              {post.downvotes}
            </Button>
          </div>
          
          <div class="flex items-center gap-1 text-gray-400">
            <MessageCircle class="h-4 w-4" />
            <span>{post.commentCount} comments</span>
          </div>
        </div>
      </div>

      <!-- Comment Form -->
      <div class="bg-dark-lighter rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">Add a Comment</h3>
        <div class="space-y-4">
          <textarea
            bind:value={newComment}
            placeholder="Share your thoughts about this deal..."
            rows="3"
            maxlength="1000"
            class="w-full px-3 py-2 bg-dark border border-gray-600 rounded-lg text-white resize-none"
          ></textarea>
          <div class="flex justify-between items-center">
            <span class="text-xs text-gray-400">{newComment.length}/1000 characters</span>
            <Button 
              on:click={submitComment} 
              disabled={!newComment.trim() || submittingComment}
              customClass="bg-primary hover:bg-primary-dark"
            >
              {submittingComment ? 'Posting...' : 'Post Comment'}
            </Button>
          </div>
        </div>
      </div>

      <!-- Comments Section -->
      <div class="space-y-4">
        <h3 class="text-lg font-semibold">Comments ({comments.length})</h3>
        
        {#each comments as comment (comment.id)}
          <div class="bg-dark-lighter rounded-lg p-4">
            <!-- Comment Header -->
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center gap-2">
                <span class="font-medium">{comment.authorUsername}</span>
                <span class="text-gray-400 text-sm">{formatTimeAgo(comment.createdAt)}</span>
              </div>
            </div>

            <!-- Comment Content -->
            <p class="mb-3 whitespace-pre-wrap">{comment.content}</p>

            <!-- Comment Actions -->
            <div class="flex items-center gap-4">
              <div class="flex items-center gap-2">
                <Button
                  on:click={() => handleCommentVote(comment.id, true)}
                  variant="secondary"
                  customClass="flex items-center gap-1 {comment.hasUpvoted ? 'text-orange-400' : 'text-gray-400'}"
                >
                  <ChevronUp class="h-3 w-3" />
                  {comment.upvotes}
                </Button>
                <Button
                  on:click={() => handleCommentVote(comment.id, false)}
                  variant="secondary"
                  customClass="flex items-center gap-1 {comment.hasDownvoted ? 'text-blue-400' : 'text-gray-400'}"
                >
                  <ChevronDown class="h-3 w-3" />
                  {comment.downvotes}
                </Button>
              </div>
              
              <Button
                on:click={() => replyingTo = replyingTo === comment.id ? null : comment.id}
                variant="secondary"
                customClass="text-gray-400 text-sm"
              >
                Reply
              </Button>
            </div>

            <!-- Reply Form -->
            {#if replyingTo === comment.id}
              <div class="mt-4 pl-4 border-l-2 border-gray-600">
                <textarea
                  bind:value={replyContent}
                  placeholder="Write a reply..."
                  rows="2"
                  maxlength="1000"
                  class="w-full px-3 py-2 bg-dark border border-gray-600 rounded-lg text-white resize-none mb-2"
                ></textarea>
                <div class="flex justify-between items-center">
                  <span class="text-xs text-gray-400">{replyContent.length}/1000 characters</span>
                  <div class="flex gap-2">
                    <Button 
                      on:click={() => { replyingTo = null; replyContent = ''; }}
                      variant="secondary"
                      customClass="text-sm"
                    >
                      Cancel
                    </Button>
                    <Button 
                      on:click={() => submitReply(comment.id)}
                      disabled={!replyContent.trim()}
                      customClass="bg-primary hover:bg-primary-dark text-sm"
                    >
                      Reply
                    </Button>
                  </div>
                </div>
              </div>
            {/if}
          </div>
        {/each}

        {#if comments.length === 0}
          <div class="text-center py-8 text-gray-400">
            <MessageCircle class="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No comments yet. Be the first to share your thoughts!</p>
          </div>
        {/if}
      </div>
    {:else}
      <div class="text-center py-12">
        <p class="text-gray-400">Post not found</p>
      </div>
    {/if}
  </div>
</div>

<!-- Username Setup Modal -->
<UsernameSetupModal 
  show={showUsernameModal} 
  on:close={() => showUsernameModal = false} 
  on:success={handleUsernameCreated} 
/>

<!-- Auth Modal -->
{#if showAuthModal}
  <AuthModal on:close={() => showAuthModal = false} />
{/if}
