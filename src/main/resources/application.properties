spring.application.name=scraper
scrapingbee.api.key=********************************************************************************
#supabase.jwt.secret=HIFZ2SPkJdqm/6AORj6BwpayERtGVhk88MplQDPcbTbF4EsUZwo7oc1R3G8rNY3Us+6oS93V9cpLn6OKXLyaAg==
#supabase.url=https://sopqivjtprerkbfsnfna.supabase.co/auth/v1
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=scraping
mailjet.api.secret=********************************
mailjet.api.key=********************************
mailjet.api.url=https://api.mailjet.com/v3.1/send
spring.data.mongodb.uri=mongodb+srv://maplecantech:<EMAIL>/scraping
google.cloud.vision.credentials=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
# Enable CORS globally
spring.web.cors.allowed-origins=https://bargainhawk.ca,http://localhost:5173,http://localhost:5174,http://localhost:5175
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=Content-Type,Accept
spring.web.cors.allow-credentials=true

#dev
# supabase.jwt.secret=iVwQfKKcauEr8KCbVLdc/0WP32IhcPm0yCBjKPHE+dpjiC4hMGZ26IlXpFvnR5GpCLgXzrtkueCO+L7p0Ug/tw==
# supabase.url=https://tovaxwkyjnrfssqzpmcl.supabase.co/auth/v1