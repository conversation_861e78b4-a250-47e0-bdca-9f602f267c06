package com.maplecan.scraper.job;

import com.maplecan.scraper.model.Item;
import com.maplecan.scraper.repository.ItemRepository;
import com.maplecan.scraper.repository.ScrapedDataRepository;
import com.maplecan.scraper.model.ScrapedData;
import com.maplecan.scraper.model.email.EmailRequest;
import com.maplecan.scraper.service.EmailService;
import com.maplecan.scraper.service.EmailTemplateFactory;
import com.maplecan.scraper.service.ItemService;
import com.maplecan.scraper.service.ScrapingBeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class PriceCheckerJob {

    @Autowired
    private ScrapingBeeService scrapingBeeService;

    @Autowired
    private ScrapedDataRepository scrapedDataRepository;

    @Autowired
    private ItemService itemService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmailTemplateFactory emailTemplateFactory;

//    @Scheduled(cron = "0 0 10 * * *") // Run daily at 10:00 AM UTC
    @Scheduled(cron = "0 * * * * *") // Run daily at 10:00 AM UTC
    public void checkPrices() {
        System.out.println("Starting price check...");

        List<ScrapedData> scrapedDataList = scrapedDataRepository.findAll();
        for (ScrapedData scrapedData : scrapedDataList) {
            processScrapedData(scrapedData);
        }

        System.out.println("Price check completed.");
    }

    private void processScrapedData(ScrapedData scrapedData) {
        try {
            Optional<Item> optionalItem = itemService.getByUrl(scrapedData.getUrl());
            if (optionalItem.isEmpty()) {
                System.out.printf("Item not found for %s%n", scrapedData.getUrl());
                return;
            }

            Item item = optionalItem.get();
            if (item.getIsAvailable() == null) {
                return;
            }
            if (!item.getIsAvailable()) {
                scrapedData.setIsAvailable(false);
                scrapingBeeService.save(scrapedData);
                System.out.printf("Item not available for %s%n. Updated is available", scrapedData.getUrl());
                return;
            }

            if (scrapedData.getIsAvailable() == null) {
                scrapedData.setIsAvailable(true);
            }
            updateScrapedDataFromItem(scrapedData, item);
            scrapingBeeService.save(scrapedData);
        } catch (Exception e) {
            System.err.printf("Failed to check price for %s: %s%n", scrapedData.getUrl(), e.getMessage());
        }
    }


    private void updateScrapedDataFromItem(ScrapedData scrapedData, Item item) {
        boolean isAvailableChange = item.getIsAvailable() && !scrapedData.getIsAvailable();
        if (!Objects.equals(item.getLatestPrice(), scrapedData.getPrice())) {
            System.out.printf("Price changed for %s! New price: %.2f (was: %.2f)%n",
                    scrapedData.getUrl(), item.getLatestPrice(), scrapedData.getPrice());
            scrapedData.setPrice(item.getLatestPrice());
        }

        if (isAvailableChange) {
            System.out.printf("Item available but scraped data wasn't available for %s",
                    scrapedData.getUrl());
            scrapedData.setIsAvailable(item.getIsAvailable());
        }

        checkForPriceDrop(scrapedData, item.getLatestPrice());
        processAvailableChange(scrapedData, item, isAvailableChange);
    }

    private void processAvailableChange(ScrapedData scrapedData, Item item, boolean isAvailableChange) {
        if (!isAvailableChange) return;
        sendAvailabilityEmail(scrapedData);
    }

    private void updateScrapedDataFromScraper(ScrapedData scrapedData, Item item) {
        ScrapedData scrapedDataNew = scrapingBeeService.fetch(scrapedData.getUrl(), scrapedData.getProvince());
        if (!Objects.equals(scrapedDataNew.getPrice(), scrapedData.getPrice())) {
            System.out.printf("Price changed for %s! New price: %.2f (was: %.2f)%n",
                    scrapedData.getUrl(), scrapedDataNew.getPrice(), scrapedData.getPrice());
            scrapedData.setPrice(scrapedDataNew.getPrice());
        }

        checkForPriceDrop(scrapedData, scrapedDataNew.getPrice());

        // Update the item's latest price and last scan time
        item.setLastScanTime(LocalDateTime.now());
        item.setLatestPrice(scrapedDataNew.getPrice());
        itemService.save(item);
    }

    private void checkForPriceDrop(ScrapedData scrapedData, Double newPrice) {
        if (newPrice >= scrapedData.getUserPrice()) {
            return;
        }

        if (scrapedData.getEmailTime() != null && scrapedData.getEmailTime().isAfter(LocalDateTime.now().minusDays(5))) {
            System.out.printf("Skipping email for %s as last email was sent recently.%n", scrapedData.getUrl());
            return;
        }

        System.out.printf("Price dropped for %s! New price: %.2f (was: %.2f)%n",
                scrapedData.getUrl(), newPrice, scrapedData.getUserPrice());

        sendPriceDropEmail(scrapedData, newPrice);
        scrapedData.setEmailTime(LocalDateTime.now());
    }

    private void sendPriceDropEmail(ScrapedData scrapedData, Double newPrice) {
        // Use factory to create email with A/B testing (50% legacy, 50% enhanced)
        EmailRequest emailRequest = emailTemplateFactory.createPriceDropEmail(scrapedData, newPrice);
        emailService.sendEmail(emailRequest);

        // Log which template was used for analytics
        String templateType = emailRequest.getSubject().contains("Target Price Reached") ? "ENHANCED" : "LEGACY";
        System.out.printf("Sent %s price drop email for %s to %s%n",
                templateType, scrapedData.getProductName(), scrapedData.getEmail());
    }

    private void sendAvailabilityEmail(ScrapedData scrapedData) {
        // Use factory to create email with A/B testing (50% legacy, 50% enhanced)
        EmailRequest emailRequest = emailTemplateFactory.createAvailabilityEmail(scrapedData);
        emailService.sendEmail(emailRequest);

        // Log which template was used for analytics
        String templateType = emailRequest.getSubject().contains("🎯") ? "ENHANCED" : "LEGACY";
        System.out.printf("Sent %s availability email for %s to %s%n",
                templateType, scrapedData.getProductName(), scrapedData.getEmail());
    }
}
