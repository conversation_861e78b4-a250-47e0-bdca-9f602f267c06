package com.maplecan.scraper.job;

import com.maplecan.scraper.model.Item;
import com.maplecan.scraper.repository.ItemRepository;
import com.maplecan.scraper.repository.ScrapedDataRepository;
import com.maplecan.scraper.model.ScrapedData;
import com.maplecan.scraper.model.email.EmailRequest;
import com.maplecan.scraper.service.EmailService;
import com.maplecan.scraper.service.ItemService;
import com.maplecan.scraper.service.ScrapingBeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class PriceCheckerJob {

    @Autowired
    private ScrapingBeeService scrapingBeeService;

    @Autowired
    private ScrapedDataRepository scrapedDataRepository;

    @Autowired
    private ItemService itemService;

    @Autowired
    private EmailService emailService;

    @Scheduled(cron = "0 0 10 * * *") // Run daily at 10:00 AM UTC
    public void checkPrices() {
        System.out.println("Starting price check...");

        List<ScrapedData> scrapedDataList = scrapedDataRepository.findAll();
        for (ScrapedData scrapedData : scrapedDataList) {
            processScrapedData(scrapedData);
        }

        System.out.println("Price check completed.");
    }

    private void processScrapedData(ScrapedData scrapedData) {
        try {
            Optional<Item> optionalItem = itemService.getByUrl(scrapedData.getUrl());
            if (optionalItem.isEmpty()) {
                System.out.printf("Item not found for %s%n", scrapedData.getUrl());
                return;
            }

            Item item = optionalItem.get();
            if (item.getIsAvailable() == null) {
                return;
            }
            if (!item.getIsAvailable()) {
                scrapedData.setIsAvailable(false);
                scrapingBeeService.save(scrapedData);
                System.out.printf("Item not available for %s%n. Updated is available", scrapedData.getUrl());
                return;
            }

            if (scrapedData.getIsAvailable() == null) {
                scrapedData.setIsAvailable(true);
            }
            updateScrapedDataFromItem(scrapedData, item);
            scrapingBeeService.save(scrapedData);
        } catch (Exception e) {
            System.err.printf("Failed to check price for %s: %s%n", scrapedData.getUrl(), e.getMessage());
        }
    }


    private void updateScrapedDataFromItem(ScrapedData scrapedData, Item item) {
        boolean isAvailableChange = item.getIsAvailable() && !scrapedData.getIsAvailable();
        if (!Objects.equals(item.getLatestPrice(), scrapedData.getPrice())) {
            System.out.printf("Price changed for %s! New price: %.2f (was: %.2f)%n",
                    scrapedData.getUrl(), item.getLatestPrice(), scrapedData.getPrice());
            scrapedData.setPrice(item.getLatestPrice());
        }

        if (isAvailableChange) {
            System.out.printf("Item available but scraped data wasn't available for %s",
                    scrapedData.getUrl());
            scrapedData.setIsAvailable(item.getIsAvailable());
        }

        checkForPriceDrop(scrapedData, item.getLatestPrice());
        processAvailableChange(scrapedData, item, isAvailableChange);
    }

    private void processAvailableChange(ScrapedData scrapedData, Item item, boolean isAvailableChange) {
        if (!isAvailableChange) return;
        sendAvailabilityEmail(scrapedData);
    }

    private void updateScrapedDataFromScraper(ScrapedData scrapedData, Item item) {
        ScrapedData scrapedDataNew = scrapingBeeService.fetch(scrapedData.getUrl(), scrapedData.getProvince());
        if (!Objects.equals(scrapedDataNew.getPrice(), scrapedData.getPrice())) {
            System.out.printf("Price changed for %s! New price: %.2f (was: %.2f)%n",
                    scrapedData.getUrl(), scrapedDataNew.getPrice(), scrapedData.getPrice());
            scrapedData.setPrice(scrapedDataNew.getPrice());
        }

        checkForPriceDrop(scrapedData, scrapedDataNew.getPrice());

        // Update the item's latest price and last scan time
        item.setLastScanTime(LocalDateTime.now());
        item.setLatestPrice(scrapedDataNew.getPrice());
        itemService.save(item);
    }

    private void checkForPriceDrop(ScrapedData scrapedData, Double newPrice) {
        if (newPrice >= scrapedData.getUserPrice()) {
            return;
        }

        if (scrapedData.getEmailTime() != null && scrapedData.getEmailTime().isAfter(LocalDateTime.now().minusDays(5))) {
            System.out.printf("Skipping email for %s as last email was sent recently.%n", scrapedData.getUrl());
            return;
        }

        System.out.printf("Price dropped for %s! New price: %.2f (was: %.2f)%n",
                scrapedData.getUrl(), newPrice, scrapedData.getUserPrice());

        sendPriceDropEmail(scrapedData, newPrice);
        scrapedData.setEmailTime(LocalDateTime.now());
    }

    private void sendPriceDropEmail(ScrapedData scrapedData, Double newPrice) {
        String subject = "Price Drop Alert for Your Tracked Item!";
        String textContent = String.format(
                "Hello %s,\n\nGreat news! The price for your tracked item '%s' has dropped.\n\n" +
                        "Product: %s\nNew Price: %.2f\nYour Target Price: %.2f\n\n" +
                        "Thank you for using BargainHawk (https://bargainhawk.ca).\n\n" +
                        "You can unsubscribe at any time: <unsubscribe-link>",
                scrapedData.getEmail(), scrapedData.getProductName(), scrapedData.getUrl(),
                newPrice, scrapedData.getUserPrice()
        );

        String htmlContent = String.format(
                "<p>Hello %s,</p>" +
                        "<p>Great news! The price for your tracked item <strong><a href=\"%s\">%s</a></strong> has dropped.</p>" +
                        "<p><strong>New Price:</strong> %.2f<br><strong>Your Target Price:</strong> %.2f</p>" +
                        "<p>Thank you for using <a href=\"https://bargainhawk.ca\">BargainHawk</a>.</p>" +
                        "<footer><p><a href=\"unsubscribe-link\">Unsubscribe</a></p></footer>",
                scrapedData.getEmail(), scrapedData.getUrl(), scrapedData.getProductName(),
                newPrice, scrapedData.getUserPrice()
        );

        EmailRequest emailRequest = EmailRequest.builder()
                .fromEmail("<EMAIL>")
                .fromName("BargainHawk")
                .toEmail(scrapedData.getEmail())
                .toName("User")
                .subject(subject)
                .textContent(textContent)
                .htmlContent(htmlContent)
                .build();

        emailService.sendEmail(emailRequest);
    }

    private void sendAvailabilityEmail(ScrapedData scrapedData) {
        String subject = "Your Tracked Item is now available!";
        String textContent = String.format(
                "Hello %s,\n\nGreat news! Your tracked item '%s' is now available.\n\n",
                scrapedData.getEmail(), scrapedData.getProductName()
        );

        String htmlContent = String.format(
                "<p>Hello %s,</p>" +
                        "<p>Great news! Your tracked item <strong><a href=\"%s\">%s</a></strong> is now available.</p>" +
                        "<p>Thank you for using <a href=\"https://bargainhawk.ca\">BargainHawk</a>.</p>",
                scrapedData.getEmail(), scrapedData.getUrl(), scrapedData.getProductName()
        );

        EmailRequest emailRequest = EmailRequest.builder()
                .fromEmail("<EMAIL>")
                .fromName("BargainHawk")
                .toEmail(scrapedData.getEmail())
                .toName("User")
                .subject(subject)
                .textContent(textContent)
                .htmlContent(htmlContent)
                .build();

        emailService.sendEmail(emailRequest);
    }
}
