package com.maplecan.scraper.service;

import com.maplecan.scraper.model.email.EmailRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class EmailService {

    @Value("${mailjet.api.url}")
    private String mailjetApiUrl;

    @Value("${mailjet.api.key}")
    private String mailjetApiKey;

    @Value("${mailjet.api.secret}")
    private String mailjetApiSecret;

    private final RestTemplate restTemplate;

    public EmailService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public void sendEmail(EmailRequest emailRequest) {
        Map<String, Object> message = new HashMap<>();
        message.put("From", Map.of("Email", emailRequest.getFromEmail(), "Name", emailRequest.getFromName()));
        message.put("To", List.of(Map.of("Email", emailRequest.getToEmail(), "Name", emailRequest.getToName())));
        message.put("Subject", emailRequest.getSubject());
        message.put("TextPart", emailRequest.getTextContent());
        message.put("HTMLPart", emailRequest.getHtmlContent());

        // Add headers to improve deliverability and avoid promotions tab
        Map<String, String> headers = new HashMap<>();
        headers.put("X-Priority", "3"); // Normal priority (1=High, 3=Normal, 5=Low)
        headers.put("X-MSMail-Priority", "Normal");
        headers.put("Importance", "Normal");
        headers.put("X-Mailer", "BargainHawk Price Monitor");
        headers.put("List-Unsubscribe", "<mailto:<EMAIL>>, <https://bargainhawk.ca/unsubscribe>");
        headers.put("List-Unsubscribe-Post", "List-Unsubscribe=One-Click");
        message.put("Headers", headers);

        Map<String, Object> body = new HashMap<>();
        body.put("Messages", List.of(message));

        restTemplate.postForEntity(mailjetApiUrl, new org.springframework.http.HttpEntity<>(body, createHeaders()), String.class);
    }

    private org.springframework.http.HttpHeaders createHeaders() {
        String auth = mailjetApiKey + ":" + mailjetApiSecret;
        String encodedAuth = java.util.Base64.getEncoder().encodeToString(auth.getBytes());
        org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
        headers.add("Authorization", "Basic " + encodedAuth);
        headers.add("Content-Type", "application/json");
        return headers;
    }
}

