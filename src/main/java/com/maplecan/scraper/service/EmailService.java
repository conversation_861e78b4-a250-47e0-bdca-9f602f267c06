package com.maplecan.scraper.service;

import com.maplecan.scraper.model.email.EmailRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class EmailService {

    @Value("${mailjet.api.url}")
    private String mailjetApiUrl;

    @Value("${mailjet.api.key}")
    private String mailjetApiKey;

    @Value("${mailjet.api.secret}")
    private String mailjetApiSecret;

    private final RestTemplate restTemplate;

    public EmailService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public void sendEmail(EmailRequest emailRequest) {
        Map<String, Object> body = new HashMap<>();
        body.put("Messages", List.of(Map.of(
                "From", Map.of("Email", emailRequest.getFromEmail(), "Name", emailRequest.getFromName()),
                "To", List.of(Map.of("Email", emailRequest.getToEmail(), "Name", emailRequest.getToName())),
                "Subject", emailRequest.getSubject(),
                "TextPart", emailRequest.getTextContent(),
                "HTMLPart", emailRequest.getHtmlContent()
        )));

        restTemplate.postForEntity(mailjetApiUrl, new org.springframework.http.HttpEntity<>(body, createHeaders()), String.class);
    }

    private org.springframework.http.HttpHeaders createHeaders() {
        String auth = mailjetApiKey + ":" + mailjetApiSecret;
        String encodedAuth = java.util.Base64.getEncoder().encodeToString(auth.getBytes());
        org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
        headers.add("Authorization", "Basic " + encodedAuth);
        headers.add("Content-Type", "application/json");
        return headers;
    }
}

