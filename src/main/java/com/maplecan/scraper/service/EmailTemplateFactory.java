package com.maplecan.scraper.service;

import com.maplecan.scraper.model.ScrapedData;
import com.maplecan.scraper.model.email.EmailRequest;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.text.NumberFormat;
import java.util.Locale;

@Service
public class EmailTemplateFactory {

    private final SecureRandom random = new SecureRandom();

    public EmailRequest createPriceDropEmail(ScrapedData scrapedData, Double newPrice) {
        // A/B test: 50% chance for each template
        boolean useEnhancedTemplate = random.nextBoolean();

        if (useEnhancedTemplate) {
            return createEnhancedPriceDropEmail(scrapedData, newPrice);
        } else {
//            return createLegacyPriceDropEmail(scrapedData, newPrice);
            return createEnhancedPriceDropEmail(scrapedData, newPrice);
        }
    }

    public EmailRequest createAvailabilityEmail(ScrapedData scrapedData) {
        // A/B test: 50% chance for each template
        boolean useEnhancedTemplate = random.nextBoolean();

        if (useEnhancedTemplate) {
            return createEnhancedAvailabilityEmail(scrapedData);
        } else {
            return createLegacyAvailabilityEmail(scrapedData);
        }
    }
    
    private EmailRequest createLegacyPriceDropEmail(ScrapedData scrapedData, Double newPrice) {
        String subject = "Price Drop Alert for Your Tracked Item!";
        String textContent = String.format(
                "Hello %s,\n\nGreat news! The price for your tracked item '%s' has dropped.\n\n" +
                        "Product: %s\nNew Price: %.2f\nYour Target Price: %.2f\n\n" +
                        "Thank you for using BargainHawk (https://bargainhawk.ca).\n\n" +
                        "You can unsubscribe at any time: <unsubscribe-link>",
                scrapedData.getEmail(), scrapedData.getProductName(), scrapedData.getUrl(),
                newPrice, scrapedData.getUserPrice()
        );

        String htmlContent = String.format(
                "<p>Hello %s,</p>" +
                        "<p>Great news! The price for your tracked item <strong><a href=\"%s\">%s</a></strong> has dropped.</p>" +
                        "<p><strong>New Price:</strong> %.2f<br><strong>Your Target Price:</strong> %.2f</p>" +
                        "<p>Thank you for using <a href=\"https://bargainhawk.ca\">BargainHawk</a>.</p>" +
                        "<footer><p><a href=\"unsubscribe-link\">Unsubscribe</a></p></footer>",
                scrapedData.getEmail(), scrapedData.getUrl(), scrapedData.getProductName(),
                newPrice, scrapedData.getUserPrice()
        );

        return EmailRequest.builder()
                .fromEmail("<EMAIL>")
                .fromName("BargainHawk")
                .toEmail(scrapedData.getEmail())
                .toName("User")
                .subject(subject)
                .textContent(textContent)
                .htmlContent(htmlContent)
                .build();
    }
    
    private EmailRequest createEnhancedPriceDropEmail(ScrapedData scrapedData, Double newPrice) {
        NumberFormat currencyFormat = NumberFormat.getCurrencyInstance(Locale.CANADA);
        String formattedNewPrice = currencyFormat.format(newPrice);
        String formattedTargetPrice = currencyFormat.format(scrapedData.getUserPrice());

        String subject = "Price Alert: " + scrapedData.getProductName() + " - Target Price Reached";
        
        String textContent = String.format(
                "Hello,\n\n" +
                        "Your price tracking alert has been triggered. The item you're monitoring has reached your target price.\n\n" +
                        "Product: %s\n" +
                        "Current Price: %s\n" +
                        "Your Target Price: %s\n\n" +
                        "View Product: %s\n\n" +
                        "Best regards,\n" +
                        "BargainHawk Price Monitoring Service\n\n" +
                        "Manage your alerts: https://bargainhawk.ca/dashboard\n" +
                        "Unsubscribe: <unsubscribe-link>",
                scrapedData.getProductName(), formattedNewPrice, formattedTargetPrice, scrapedData.getUrl()
        );

        String htmlContent = createEnhancedHtmlTemplate(scrapedData, newPrice, formattedNewPrice, formattedTargetPrice);

        return EmailRequest.builder()
                .fromEmail("<EMAIL>")
                .fromName("BargainHawk Price Monitoring")
                .toEmail(scrapedData.getEmail())
                .toName("Customer")
                .subject(subject)
                .textContent(textContent)
                .htmlContent(htmlContent)
                .build();
    }
    
    private String createEnhancedHtmlTemplate(ScrapedData scrapedData, Double newPrice,
            String formattedNewPrice, String formattedTargetPrice) {
        
        String productImage = scrapedData.getImageUrl() != null ? scrapedData.getImageUrl() : 
                "https://via.placeholder.com/300x300?text=Product+Image";
        
        return String.format("""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Price Drop Alert</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
                    .container { max-width: 600px; margin: 0 auto; background-color: white; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    .header { background: linear-gradient(135deg, #3b82f6 0%%, #1e40af 100%%); color: white; padding: 30px 20px; text-align: center; }
                    .header h1 { margin: 0; font-size: 28px; font-weight: bold; }
                    .header p { margin: 10px 0 0 0; font-size: 16px; opacity: 0.9; }
                    .content { padding: 30px 20px; }
                    .alert-badge { background-color: #ef4444; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: bold; display: inline-block; margin-bottom: 20px; }
                    .product-card { border: 1px solid #e1e8ed; border-radius: 12px; overflow: hidden; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                    .product-image-container { width: 100%%; height: 280px; overflow: hidden; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center; }
                    .product-image { width: 100%%; height: 100%%; object-fit: contain; }
                    .product-info { padding: 20px; }
                    .product-name { font-size: 20px; font-weight: bold; color: #2c3e50; margin-bottom: 15px; line-height: 1.4; }
                    .price-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; }
                    .price-row { display: flex; justify-content: space-between; align-items: center; margin: 10px 0; }
                    .price-label { font-weight: 600; color: #6c757d; }
                    .price-value { font-weight: bold; font-size: 18px; }
                    .new-price { color: #22c55e; }
                    .old-price { color: #6c757d; }
                    .target-price { color: #8b5cf6; }
                    .savings { color: #ef4444; font-size: 20px; }
                    .cta-container { text-align: center; margin: 25px 0; }
                    .cta-button { background: linear-gradient(135deg, #3b82f6 0%%, #1e40af 100%%); color: white !important; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3); transition: all 0.3s ease; margin: 5px 10px; }
                    .cta-button:hover { transform: translateY(-1px); box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4); color: white !important; }
                    .cta-button-secondary { background: linear-gradient(135deg, #6b7280 0%%, #374151 100%%); box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3); }
                    .cta-button-secondary:hover { box-shadow: 0 4px 8px rgba(107, 114, 128, 0.4); }
                    .footer { background-color: #1f2937; color: white; padding: 30px 20px; text-align: center; }
                    .footer a { color: #60a5fa; text-decoration: none; }
                    .social-links { margin: 20px 0; }
                    .social-links a { color: #60a5fa; margin: 0 10px; text-decoration: none; }
                    @media (max-width: 600px) {
                        .price-row { flex-direction: column; align-items: flex-start; }
                        .cta-button { width: 85%%; margin: 8px 0; display: block; }
                        .product-image-container { height: 250px; }
                        .cta-container { margin: 20px 0; }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Price Alert Notification</h1>
                        <p>Your tracked item has reached your target price</p>
                    </div>

                    <div class="content">
                        <div class="alert-badge">TARGET PRICE REACHED</div>
                        
                        <div class="product-card">
                            <div class="product-image-container">
                                <img src="%s" alt="Product Image" class="product-image" onerror="this.src='https://via.placeholder.com/300x300?text=Product+Image'">
                            </div>
                            <div class="product-info">
                                <div class="product-name">%s</div>

                                <div class="price-section">
                                    <div class="price-row">
                                        <span class="price-label">Current Price:</span>
                                        <span class="price-value new-price">%s</span>
                                    </div>
                                    <div class="price-row">
                                        <span class="price-label">Your Target Price:</span>
                                        <span class="price-value target-price">%s</span>
                                    </div>
                                </div>

                                <div class="cta-container">
                                    <a href="%s" class="cta-button">View Product</a>
                                    <a href="https://bargainhawk.ca/dashboard" class="cta-button cta-button-secondary">View Your Dashboard</a>
                                </div>
                            </div>
                        </div>
                        
                        <p style="color: #6c757d; font-size: 14px; line-height: 1.6;">
                            This price drop was detected by our automated monitoring system. 
                            Prices can change frequently, so we recommend checking out soon to secure this deal!
                        </p>
                    </div>
                    
                    <div class="footer">
                        <p><strong>BargainHawk Price Monitoring Service</strong></p>
                        <div class="social-links">
                            <a href="https://bargainhawk.ca/dashboard">Manage Alerts</a> |
                            <a href="unsubscribe-link">Unsubscribe</a>
                        </div>
                        <p style="font-size: 12px; opacity: 0.8; margin-top: 20px;">
                            © 2025 BargainHawk. All rights reserved.<br>
                            This is an automated price monitoring notification for your tracked item.
                        </p>
                    </div>
                </div>
            </body>
            </html>
            """,
            productImage, scrapedData.getProductName(), formattedNewPrice, formattedTargetPrice, scrapedData.getUrl()
        );
    }

    private EmailRequest createLegacyAvailabilityEmail(ScrapedData scrapedData) {
        String subject = "Your Tracked Item is now available!";
        String textContent = String.format(
                "Hello %s,\n\nGreat news! Your tracked item '%s' is now available.\n\n",
                scrapedData.getEmail(), scrapedData.getProductName()
        );

        String htmlContent = String.format(
                "<p>Hello %s,</p>" +
                        "<p>Great news! Your tracked item <strong><a href=\"%s\">%s</a></strong> is now available.</p>" +
                        "<p>Thank you for using <a href=\"https://bargainhawk.ca\">BargainHawk</a>.</p>",
                scrapedData.getEmail(), scrapedData.getUrl(), scrapedData.getProductName()
        );

        return EmailRequest.builder()
                .fromEmail("<EMAIL>")
                .fromName("BargainHawk")
                .toEmail(scrapedData.getEmail())
                .toName("User")
                .subject(subject)
                .textContent(textContent)
                .htmlContent(htmlContent)
                .build();
    }

    private EmailRequest createEnhancedAvailabilityEmail(ScrapedData scrapedData) {
        String subject = "🎯 Back in Stock! Your Tracked Item is Available";

        String textContent = String.format(
                "Hello!\n\n🎯 GREAT NEWS! Your tracked item is back in stock!\n\n" +
                        "Product: %s\n" +
                        "Current Price: %s\n\n" +
                        "View Product: %s\n\n" +
                        "Don't wait - popular items sell out fast!\n\n" +
                        "Happy shopping!\nThe BargainHawk Team\n\n" +
                        "Visit us: https://bargainhawk.ca\n" +
                        "Unsubscribe: <unsubscribe-link>",
                scrapedData.getProductName(),
                NumberFormat.getCurrencyInstance(Locale.CANADA).format(scrapedData.getPrice()),
                scrapedData.getUrl()
        );

        String htmlContent = createEnhancedAvailabilityHtmlTemplate(scrapedData);

        return EmailRequest.builder()
                .fromEmail("<EMAIL>")
                .fromName("BargainHawk - Stock Alert")
                .toEmail(scrapedData.getEmail())
                .toName("Bargain Hunter")
                .subject(subject)
                .textContent(textContent)
                .htmlContent(htmlContent)
                .build();
    }

    private String createEnhancedAvailabilityHtmlTemplate(ScrapedData scrapedData) {
        String productImage = scrapedData.getImageUrl() != null ? scrapedData.getImageUrl() :
                "https://via.placeholder.com/300x300?text=Product+Image";
        String formattedPrice = NumberFormat.getCurrencyInstance(Locale.CANADA).format(scrapedData.getPrice());

        return String.format("""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Back in Stock Alert</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
                    .container { max-width: 600px; margin: 0 auto; background-color: white; }
                    .header { background: linear-gradient(135deg, #3b82f6 0%%, #1e40af 100%%); color: white; padding: 30px 20px; text-align: center; }
                    .header h1 { margin: 0; font-size: 28px; font-weight: bold; }
                    .header p { margin: 10px 0 0 0; font-size: 16px; opacity: 0.9; }
                    .content { padding: 30px 20px; }
                    .alert-badge { background-color: #22c55e; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: bold; display: inline-block; margin-bottom: 20px; }
                    .product-card { border: 1px solid #e1e8ed; border-radius: 12px; overflow: hidden; margin: 20px 0; }
                    .product-image { width: 100%%; height: 250px; object-fit: cover; }
                    .product-info { padding: 20px; }
                    .product-name { font-size: 20px; font-weight: bold; color: #2c3e50; margin-bottom: 15px; line-height: 1.4; }
                    .price-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; text-align: center; }
                    .current-price { font-size: 24px; font-weight: bold; color: #28a745; }
                    .cta-container { text-align: center; margin: 25px 0; }
                    .cta-button { background: linear-gradient(135deg, #3b82f6 0%%, #1e40af 100%%); color: white !important; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block; margin: 5px 10px; text-align: center; min-width: 180px; }
                    .cta-button-secondary { background: linear-gradient(135deg, #6b7280 0%%, #374151 100%%); color: white !important; }
                    .urgency-note { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0; color: #856404; }
                    .footer { background-color: #2c3e50; color: white; padding: 30px 20px; text-align: center; }
                    .footer a { color: #3498db; text-decoration: none; }
                    .social-links { margin: 20px 0; }
                    .social-links a { color: #3498db; margin: 0 10px; text-decoration: none; }
                    @media (max-width: 600px) {
                        .cta-container { margin: 20px 0; }
                        .cta-button { display: block; margin: 10px auto; width: 90%%; min-width: auto; }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🎯 Back in Stock!</h1>
                        <p>Your tracked item is available again</p>
                    </div>

                    <div class="content">
                        <div class="alert-badge">NOW AVAILABLE</div>

                        <div class="product-card">
                            <img src="%s" alt="Product Image" class="product-image" onerror="this.src='https://via.placeholder.com/300x300?text=Product+Image'">
                            <div class="product-info">
                                <div class="product-name">%s</div>

                                <div class="price-section">
                                    <div class="current-price">%s</div>
                                    <p style="margin: 5px 0 0 0; color: #6c757d;">Current Price</p>
                                </div>

                                <div class="urgency-note">
                                    ⚡ <strong>Act Fast!</strong> Popular items often sell out quickly after restocking.
                                </div>

                                <div class="cta-container">
                                    <a href="%s" class="cta-button">Shop Now</a>
                                    <a href="https://bargainhawk.ca/dashboard" class="cta-button cta-button-secondary">View Your Dashboard</a>
                                </div>
                            </div>
                        </div>

                        <p style="color: #6c757d; font-size: 14px; line-height: 1.6;">
                            This availability alert was triggered by our automated monitoring system.
                            Stock levels can change rapidly, so we recommend checking out soon!
                        </p>
                    </div>

                    <div class="footer">
                        <p><strong>BargainHawk</strong> - Your Smart Shopping Companion</p>
                        <div class="social-links">
                            <a href="https://bargainhawk.ca">Visit Website</a> |
                            <a href="https://bargainhawk.ca/price-drops">View All Deals</a> |
                            <a href="unsubscribe-link">Unsubscribe</a>
                        </div>
                        <p style="font-size: 12px; opacity: 0.8; margin-top: 20px;">
                            © 2025 BargainHawk. All rights reserved.<br>
                            You're receiving this because you're tracking availability for this item.
                        </p>
                    </div>
                </div>
            </body>
            </html>
            """,
            productImage, scrapedData.getProductName(), formattedPrice, scrapedData.getUrl()
        );
    }
}
