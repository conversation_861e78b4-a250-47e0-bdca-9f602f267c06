package com.maplecan.scraper.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service to parse Walmart URLs and extract item IDs
 */
@Slf4j
@Service
public class WalmartUrlParser {

    // Regex patterns for different Walmart URL formats
    private static final Pattern IP_PATTERN = Pattern.compile("/ip/([^/]+)/(\\d+)");
    private static final Pattern SEOID_PATTERN = Pattern.compile("/([^/]+)/(\\d+)$");
    private static final Pattern ITEM_ID_PATTERN = Pattern.compile("/(\\d{8,})(?:[/?]|$)");
    
    /**
     * Extracts item ID from various Walmart URL formats
     * 
     * Supported URL formats:
     * - https://www.walmart.com/ip/Product-Name/4837473
     * - https://www.walmart.com/ip/4837473
     * - https://walmart.com/ip/Product-Name/4837473
     * - https://www.walmart.com/Product-Name/4837473
     * 
     * @param url Walmart product URL
     * @return Item ID as string, or null if not found
     */
    public String extractItemId(String url) {
        if (url == null || url.trim().isEmpty()) {
            log.warn("URL is null or empty");
            return null;
        }

        try {
            // Normalize URL
            String normalizedUrl = normalizeUrl(url);
            log.debug("Normalized URL: {}", normalizedUrl);

            // Try different patterns in order of specificity
            String itemId = tryIpPattern(normalizedUrl);
            if (itemId != null) {
                log.info("Extracted item ID {} from URL using IP pattern: {}", itemId, url);
                return itemId;
            }

            itemId = trySeoIdPattern(normalizedUrl);
            if (itemId != null) {
                log.info("Extracted item ID {} from URL using SEO ID pattern: {}", itemId, url);
                return itemId;
            }

            itemId = tryGenericItemIdPattern(normalizedUrl);
            if (itemId != null) {
                log.info("Extracted item ID {} from URL using generic pattern: {}", itemId, url);
                return itemId;
            }

            log.warn("Could not extract item ID from URL: {}", url);
            return null;

        } catch (Exception e) {
            log.error("Error parsing Walmart URL {}: {}", url, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Validates if a URL is a Walmart product URL
     */
    public boolean isWalmartUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }

        try {
            String normalizedUrl = normalizeUrl(url);
            return normalizedUrl.contains("walmart.com") && 
                   (normalizedUrl.contains("/ip/") || containsItemId(normalizedUrl));
        } catch (Exception e) {
            log.debug("Error validating Walmart URL {}: {}", url, e.getMessage());
            return false;
        }
    }

    /**
     * Normalizes URL for consistent parsing
     */
    private String normalizeUrl(String url) {
        // Remove protocol and www
        String normalized = url.toLowerCase()
                .replaceFirst("^https?://", "")
                .replaceFirst("^www\\.", "");

        // Ensure it starts with walmart.com
        if (!normalized.startsWith("walmart.com")) {
            if (normalized.startsWith("walmart.com")) {
                // Already correct
            } else {
                throw new IllegalArgumentException("Not a Walmart URL: " + url);
            }
        }

        return normalized;
    }

    /**
     * Try to extract using /ip/Product-Name/ItemID pattern
     */
    private String tryIpPattern(String url) {
        Matcher matcher = IP_PATTERN.matcher(url);
        if (matcher.find()) {
            return matcher.group(2); // Item ID is the second group
        }
        return null;
    }

    /**
     * Try to extract using /Product-Name/ItemID pattern (SEO URLs)
     */
    private String trySeoIdPattern(String url) {
        Matcher matcher = SEOID_PATTERN.matcher(url);
        if (matcher.find()) {
            String potentialId = matcher.group(2);
            // Validate it's a reasonable item ID (8+ digits)
            if (potentialId.length() >= 7) {
                return potentialId;
            }
        }
        return null;
    }

    /**
     * Try to extract any 8+ digit number that could be an item ID
     */
    private String tryGenericItemIdPattern(String url) {
        Matcher matcher = ITEM_ID_PATTERN.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * Check if URL contains what looks like an item ID
     */
    private boolean containsItemId(String url) {
        return ITEM_ID_PATTERN.matcher(url).find();
    }

    /**
     * Validates if an extracted item ID looks reasonable
     */
    public boolean isValidItemId(String itemId) {
        if (itemId == null || itemId.trim().isEmpty()) {
            return false;
        }

        try {
            long id = Long.parseLong(itemId);
            // Walmart item IDs are typically 7-10 digits
            return id >= 1000000 && id <= 99999999999L;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Extracts and validates item ID from URL
     */
    public String extractAndValidateItemId(String url) {
        String itemId = extractItemId(url);
        if (itemId != null && isValidItemId(itemId)) {
            return itemId;
        }
        return null;
    }
}
