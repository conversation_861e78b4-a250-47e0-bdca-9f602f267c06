package com.maplecan.scraper.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class CreateUserProfileRequest {
    
    @NotBlank(message = "Username is required")
    @Size(min = 3, max = 36, message = "Username must be between 3 and 36 characters")
    private String username;
    
    @Size(max = 50, message = "First name cannot exceed 50 characters")
    private String firstName;
    
    @Size(max = 50, message = "Last name cannot exceed 50 characters")
    private String lastName;
}
