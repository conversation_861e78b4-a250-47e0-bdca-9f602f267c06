package com.maplecan.scraper.model;

import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;
import java.util.List;

@Document(collection = "community_posts")
@Data
@Builder
public class CommunityPost {
    @Id
    private String id;
    
    @Indexed
    private String authorId; // Supabase user ID
    
    private String title;
    private String content;
    private String productUrl;
    private String productName;
    private String productImage;
    private Double originalPrice;
    private Double currentPrice;
    private String store; // costco, walmart, wayfair
    private String category; // hot-deals, price-drops, general
    
    @Indexed
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Engagement metrics
    private Integer upvotes;
    private Integer downvotes;
    private Integer commentCount;
    private Integer viewCount;
    
    // Lists to track who voted
    private List<String> upvotedBy; // List of user IDs who upvoted
    private List<String> downvotedBy; // List of user IDs who downvoted
    
    // Moderation
    private Boolean isActive;
    private Boolean isFeatured;
    private List<String> tags;
    
    public CommunityPost() {
        this.upvotes = 0;
        this.downvotes = 0;
        this.commentCount = 0;
        this.viewCount = 0;
        this.isActive = true;
        this.isFeatured = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
