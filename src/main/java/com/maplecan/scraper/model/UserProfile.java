package com.maplecan.scraper.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;

@Document(collection = "user_profiles")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserProfile {
    @Id
    private String id;
    
    @Indexed(unique = true)
    private String supabaseUserId; // Primary key from Supabase
    
    @Indexed(unique = true)
    private String username; // Max 36 characters, unique
    
    private String firstName;
    private String lastName;
    private String email;
    private String avatarUrl;

    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    // Community stats
    @Builder.Default
    private Integer totalPosts = 0;
    @Builder.Default
    private Integer totalComments = 0;
    @Builder.Default
    private Integer totalUpvotes = 0;
    @Builder.Default
    private Integer totalDownvotes = 0;
    @Builder.Default
    private Integer reputationScore = 0;

}
