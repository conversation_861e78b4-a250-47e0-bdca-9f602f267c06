package com.maplecan.scraper.model;

import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;

@Document(collection = "user_profiles")
@Data
@Builder
public class UserProfile {
    @Id
    private String id;
    
    @Indexed(unique = true)
    private String supabaseUserId; // Primary key from Supabase
    
    @Indexed(unique = true)
    private String username; // Max 36 characters, unique
    
    private String firstName;
    private String lastName;
    private String email;
    private String avatarUrl;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Community stats
    private Integer totalPosts;
    private Integer totalComments;
    private Integer totalUpvotes;
    private Integer totalDownvotes;
    private Integer reputationScore;
    
    public UserProfile() {
        this.totalPosts = 0;
        this.totalComments = 0;
        this.totalUpvotes = 0;
        this.totalDownvotes = 0;
        this.reputationScore = 0;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
