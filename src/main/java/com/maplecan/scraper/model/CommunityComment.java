package com.maplecan.scraper.model;

import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;
import java.util.List;

@Document(collection = "community_comments")
@Data
@Builder
public class CommunityComment {
    @Id
    private String id;
    
    @Indexed
    private String postId; // Reference to CommunityPost
    
    @Indexed
    private String authorId; // Supabase user ID
    
    private String content;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Engagement metrics
    private Integer upvotes;
    private Integer downvotes;
    
    // Lists to track who voted
    private List<String> upvotedBy; // List of user IDs who upvoted
    private List<String> downvotedBy; // List of user IDs who downvoted
    
    // Threading support for nested comments
    private String parentCommentId; // null for top-level comments
    private Integer depth; // 0 for top-level, 1 for replies, etc.
    
    // Moderation
    private Boolean isActive;
    
    public CommunityComment() {
        this.upvotes = 0;
        this.downvotes = 0;
        this.depth = 0;
        this.isActive = true;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
