package com.maplecan.scraper.controller;

import com.maplecan.scraper.model.AddNewProductRequest;
import com.maplecan.scraper.model.AddNewProductRequestByItemNumber;
import com.maplecan.scraper.model.ScrapedData;
import com.maplecan.scraper.model.ScrapedDataPatchRequest;
import com.maplecan.scraper.service.ScrapingBeeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@RestController
public class ScraperController {

    private final ScrapingBeeService scrapingBeeService;

    public ScraperController(ScrapingBeeService scrapingBeeService) {
        this.scrapingBeeService = scrapingBeeService;
    }

    @GetMapping("/fetch-url")
    public ScrapedData fetchUrl(@RequestParam String url, @RequestParam String province) {
        return scrapingBeeService.fetchUrlAndSave(url, province);
    }

    @GetMapping("/v2/fetch-url")
    public ScrapedData fetchUrlV2(@RequestParam String url, @RequestParam String province) {
        return scrapingBeeService.fetch(url, province);
    }

    @PostMapping("/fetch-url")
    public ResponseEntity<Object> fetchUrl(@RequestBody AddNewProductRequest request) {
        String email2 = (String) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        log.info("Getting product list for {}", email2);
        request.setEmail(email2);
        List<ScrapedData> scrapedDataList = scrapingBeeService.getProductList(request.getEmail());
        if (scrapedDataList.size() > 4) {
            return ResponseEntity.badRequest().body("Cannot exceed 5 products");
        }
        return ResponseEntity.ok(scrapingBeeService.fetchUrlAndSave(request));
    }

    @PostMapping("/fetchByItem")
    public ResponseEntity<Object> fetchItemNumber(@RequestBody AddNewProductRequestByItemNumber request) {
        String email2 = (String) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        log.info("Getting product list for {}", email2);
        request.setEmail(email2);
        List<ScrapedData> scrapedDataList = scrapingBeeService.getProductList(request.getEmail());
        if (scrapedDataList.size() > 4) {
            return ResponseEntity.badRequest().body("Cannot exceed 5 products");
        }
        return ResponseEntity.ok(scrapingBeeService.fetchItemNumberAndSave(request));
    }

    @DeleteMapping("/products/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteById(@PathVariable String id) {
        scrapingBeeService.deleteById(id);
    }

    @GetMapping("/productList")
    public List<ScrapedData> fetchList(@RequestParam String email) {
        String email2 = (String) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        log.info("Getting product list for {}", email2);

        return scrapingBeeService.getProductList(email2);
    }

    @PatchMapping("/products/{id}")
    public ResponseEntity<String> updateScrapedData(@PathVariable String id,
                                                    @RequestBody ScrapedDataPatchRequest patchRequest) {
        ScrapedData existingData = scrapingBeeService.findById(id).orElse(null);

        if (existingData == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Scraped data with ID " + id + " not found.");
        }

        // Update fields based on the patch request
        if (patchRequest.getUserPrice() != null) {
            existingData.setUserPrice(patchRequest.getUserPrice());
        }

        if (patchRequest.getProvince() != null) {
            existingData.setProvince(patchRequest.getProvince());
        }

        existingData.setUpdatedTimestamp(LocalDateTime.now());
        scrapingBeeService.save(existingData);
        return ResponseEntity.ok("Scraped data updated successfully.");
    }

}
