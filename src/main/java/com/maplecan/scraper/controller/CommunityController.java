package com.maplecan.scraper.controller;

import com.maplecan.scraper.dto.*;
import com.maplecan.scraper.model.UserProfile;
import com.maplecan.scraper.service.CommunityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/community")
@Slf4j
public class CommunityController {

    @Autowired
    private CommunityService communityService;

    // User Profile Endpoints
    @PostMapping("/profile")
    public ResponseEntity<?> createUserProfile(@Valid @RequestBody CreateUserProfileRequest request) {
        try {
            UserProfile profile = communityService.createUserProfile(request);
            return ResponseEntity.ok(profile);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Error creating user profile", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to create user profile"));
        }
    }

    @GetMapping("/profile")
    public ResponseEntity<?> getUserProfile() {
        try {
            return communityService.getUserProfile()
                    .map(profile -> ResponseEntity.ok(profile))
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("Error getting user profile", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get user profile"));
        }
    }

    @GetMapping("/profile/exists")
    public ResponseEntity<Map<String, Boolean>> checkUserProfile() {
        try {
            boolean exists = communityService.hasUserProfile();
            return ResponseEntity.ok(Map.of("exists", exists));
        } catch (Exception e) {
            log.error("Error checking user profile", e);
            return ResponseEntity.ok(Map.of("exists", false));
        }
    }

    // Post Endpoints
    @PostMapping("/posts")
    public ResponseEntity<?> createPost(@Valid @RequestBody CreatePostRequest request) {
        try {
            PostResponse post = communityService.createPost(request);
            return ResponseEntity.ok(post);
        } catch (IllegalStateException e) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", e.getMessage()));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Error creating post", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to create post"));
        }
    }

    @GetMapping("/posts")
    public ResponseEntity<Page<PostResponse>> getPosts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String store,
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "new") String sort) {
        try {
            Page<PostResponse> posts = communityService.getPosts(page, size, store, category, sort);
            return ResponseEntity.ok(posts);
        } catch (Exception e) {
            log.error("Error getting posts", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/posts/{postId}")
    public ResponseEntity<?> getPost(@PathVariable String postId) {
        try {
            PostResponse post = communityService.getPost(postId);
            return ResponseEntity.ok(post);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error getting post", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get post"));
        }
    }

    // Voting Endpoints
    @PostMapping("/posts/{postId}/upvote")
    public ResponseEntity<?> upvotePost(@PathVariable String postId) {
        try {
            PostResponse post = communityService.votePost(postId, true);
            return ResponseEntity.ok(post);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (IllegalStateException e) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Error upvoting post", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to upvote post"));
        }
    }

    @PostMapping("/posts/{postId}/downvote")
    public ResponseEntity<?> downvotePost(@PathVariable String postId) {
        try {
            PostResponse post = communityService.votePost(postId, false);
            return ResponseEntity.ok(post);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (IllegalStateException e) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Error downvoting post", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to downvote post"));
        }
    }

    // Comment Endpoints
    @PostMapping("/posts/{postId}/comments")
    public ResponseEntity<?> createComment(@PathVariable String postId, @Valid @RequestBody CreateCommentRequest request) {
        try {
            CommentResponse comment = communityService.createComment(postId, request);
            return ResponseEntity.ok(comment);
        } catch (IllegalStateException e) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", e.getMessage()));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Error creating comment", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to create comment"));
        }
    }

    @GetMapping("/posts/{postId}/comments")
    public ResponseEntity<?> getComments(@PathVariable String postId) {
        try {
            List<CommentResponse> comments = communityService.getComments(postId);
            return ResponseEntity.ok(comments);
        } catch (Exception e) {
            log.error("Error getting comments", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get comments"));
        }
    }

    @PostMapping("/comments/{commentId}/upvote")
    public ResponseEntity<?> upvoteComment(@PathVariable String commentId) {
        try {
            CommentResponse comment = communityService.voteComment(commentId, true);
            return ResponseEntity.ok(comment);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (IllegalStateException e) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Error upvoting comment", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to upvote comment"));
        }
    }

    @PostMapping("/comments/{commentId}/downvote")
    public ResponseEntity<?> downvoteComment(@PathVariable String commentId) {
        try {
            CommentResponse comment = communityService.voteComment(commentId, false);
            return ResponseEntity.ok(comment);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (IllegalStateException e) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Error downvoting comment", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to downvote comment"));
        }
    }
}
