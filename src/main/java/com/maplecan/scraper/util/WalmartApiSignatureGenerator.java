package com.maplecan.scraper.util;

import java.io.IOException;
import java.io.ObjectStreamException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyRep;
import java.security.PrivateKey;
import java.security.Signature;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;

import org.apache.commons.codec.binary.Base64;

/**
 * Walmart API Signature Generator
 * Generates the required authentication headers for Walmart Affiliate API
 */
public class WalmartApiSignatureGenerator {

    private static final String CONSUMER_ID = "d7cc8a42-4c30-4ca6-82c3-5cdab55053c8";
    private static final String PRIVATE_KEY_VERSION = "1";
    private static final String PRIVATE_KEY_PATH = "/Users/<USER>/WM_IO_private_key.pem";

    public static void main(String[] args) {
        WalmartApiSignatureGenerator generator = new WalmartApiSignatureGenerator();
        
        try {
            // Generate all required headers
            Map<String, String> headers = generator.generateWalmartApiHeaders();
            
            System.out.println("=== WALMART API HEADERS ===");
            System.out.println("WM_CONSUMER.ID: " + headers.get("WM_CONSUMER.ID"));
            System.out.println("WM_CONSUMER.INTIMESTAMP: " + headers.get("WM_CONSUMER.INTIMESTAMP"));
            System.out.println("WM_SEC.KEY_VERSION: " + headers.get("WM_SEC.KEY_VERSION"));
            System.out.println("WM_SEC.AUTH_SIGNATURE: " + headers.get("WM_SEC.AUTH_SIGNATURE"));
            System.out.println("========================");
            
            // Also print in a format easy to copy for testing
            System.out.println("\n=== FOR API TESTING ===");
            System.out.println("Headers to include in your HTTP request:");
            headers.forEach((key, value) -> System.out.println(key + ": " + value));
            
        } catch (Exception e) {
            System.err.println("Error generating Walmart API headers: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Generates all required Walmart API headers
     * @return Map containing all required headers
     * @throws Exception if signature generation fails
     */
    public Map<String, String> generateWalmartApiHeaders() throws Exception {
        long timestamp = System.currentTimeMillis();
        
        Map<String, String> headers = new HashMap<>();
        headers.put("WM_CONSUMER.ID", CONSUMER_ID);
        headers.put("WM_CONSUMER.INTIMESTAMP", Long.toString(timestamp));
        headers.put("WM_SEC.KEY_VERSION", PRIVATE_KEY_VERSION);
        
        // Generate signature
        String signature = generateSignature(headers);
        headers.put("WM_SEC.AUTH_SIGNATURE", signature);
        
        return headers;
    }

    /**
     * Generates the signature for Walmart API authentication
     * @param headers Map containing the headers to sign
     * @return Base64 encoded signature
     * @throws Exception if signature generation fails
     */
    private String generateSignature(Map<String, String> headers) throws Exception {
        // Read private key from file
        String privateKeyContent = readPrivateKeyFromFile();
        
        // Canonicalize the headers
        String[] canonicalizedData = canonicalize(headers);
        String stringToSign = canonicalizedData[1];
        
        System.out.println("String to sign: " + stringToSign.replace("\n", "\\n"));
        
        // Generate signature
        return generateSignature(privateKeyContent, stringToSign);
    }

    /**
     * Reads the private key from the PEM file
     * @return Private key content as Base64 string (without headers/footers)
     * @throws IOException if file cannot be read
     */
    private String readPrivateKeyFromFile() throws IOException {
        String content = new String(Files.readAllBytes(Paths.get(PRIVATE_KEY_PATH)));

        System.out.println("Original PEM content length: " + content.length());
        System.out.println("First 100 chars: " + content.substring(0, Math.min(100, content.length())));

        // Remove PEM headers and footers, and newlines
        String cleanedKey = content
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replace("-----BEGIN RSA PRIVATE KEY-----", "")
                .replace("-----END RSA PRIVATE KEY-----", "")
                .replaceAll("\\s", "");

        System.out.println("Cleaned key length: " + cleanedKey.length());
        System.out.println("First 50 chars of cleaned key: " + cleanedKey.substring(0, Math.min(50, cleanedKey.length())));

        return cleanedKey;
    }

    /**
     * Generates the actual signature using the private key
     * @param privateKeyBase64 Base64 encoded private key
     * @param stringToSign String to be signed
     * @return Base64 encoded signature
     * @throws Exception if signature generation fails
     */
    public String generateSignature(String privateKeyBase64, String stringToSign) throws Exception {
        System.out.println("Generating signature for string: " + stringToSign.replace("\n", "\\n"));
        System.out.println("Private key length: " + privateKeyBase64.length());

        Signature signatureInstance = Signature.getInstance("SHA256WithRSA");

        ServiceKeyRep keyRep = new ServiceKeyRep(KeyRep.Type.PRIVATE, "RSA", "PKCS#8",
                Base64.decodeBase64(privateKeyBase64));

        PrivateKey resolvedPrivateKey = (PrivateKey) keyRep.readResolve();
        System.out.println("Private key algorithm: " + resolvedPrivateKey.getAlgorithm());
        System.out.println("Private key format: " + resolvedPrivateKey.getFormat());

        signatureInstance.initSign(resolvedPrivateKey);

        byte[] bytesToSign = stringToSign.getBytes("UTF-8");
        signatureInstance.update(bytesToSign);
        byte[] signatureBytes = signatureInstance.sign();

        String signature = Base64.encodeBase64String(signatureBytes);
        System.out.println("Generated signature length: " + signature.length());
        System.out.println("Generated signature: " + signature.substring(0, Math.min(50, signature.length())) + "...");

        return signature;
    }

    /**
     * Canonicalizes the headers for signature generation
     * @param headersToSign Map of headers to sign
     * @return Array containing [parameter names, canonicalized string]
     */
    protected static String[] canonicalize(Map<String, String> headersToSign) {
        StringBuffer canonicalizedStrBuffer = new StringBuffer();
        StringBuffer parameterNamesBuffer = new StringBuffer();
        Set<String> keySet = headersToSign.keySet();

        // Create sorted key set to enforce order on the key names
        SortedSet<String> sortedKeySet = new TreeSet<>(keySet);
        for (String key : sortedKeySet) {
            Object val = headersToSign.get(key);
            parameterNamesBuffer.append(key.trim()).append(";");
            canonicalizedStrBuffer.append(val.toString().trim()).append("\n");
        }
        return new String[]{parameterNamesBuffer.toString(), canonicalizedStrBuffer.toString()};
    }

    /**
     * Custom KeyRep class for handling private key deserialization
     */
    class ServiceKeyRep extends KeyRep {
        private static final long serialVersionUID = -7213340660431987616L;

        public ServiceKeyRep(KeyRep.Type type, String algorithm, String format, byte[] encoded) {
            super(type, algorithm, format, encoded);
        }

        protected Object readResolve() throws ObjectStreamException {
            return super.readResolve();
        }
    }
}
