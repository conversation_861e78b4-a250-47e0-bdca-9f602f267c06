# 🧪 BargainHawk Extension Testing Guide

## Quick Start Testing

### 1. **Setup Extension**
```bash
cd bargainhawk-extension
npm install
npm run build
```

### 2. **Load in Chrome**
1. Open Chrome → `chrome://extensions/`
2. Enable "Developer mode" (top right toggle)
3. Click "Load unpacked"
4. Select the `dist/` folder

### 3. **Configure API Endpoint**
Edit `popup.js` line 8:
```javascript
// For local testing with your scraper running on localhost:8080
this.apiBaseUrl = 'http://localhost:8080';

// For production testing
// this.apiBaseUrl = 'https://your-scraper-app.fly.dev';
```

## 🎯 Testing Scenarios

### **Scenario 1: Costco Canada Product**
1. **Visit**: https://www.costco.ca/kirkland-signature-organic-extra-virgin-olive-oil%2c-2-l.product.*********.html
2. **Click**: BargainHawk extension icon
3. **Login**: Use your BargainHawk account
4. **Test**: Click "Auto-Detect Product"
5. **Expected**: Should detect product name, price, and image
6. **Add**: Set target price and click "Add to BargainHawk"

### **Scenario 2: Walmart Canada Product**
1. **Visit**: https://www.walmart.ca/en/ip/great-value-peanut-butter-smooth-1-kg/*************
2. **Follow**: Same steps as Costco
3. **Expected**: Should work with Walmart's price structure

### **Scenario 3: Manual Price Selection**
1. **Visit**: Any supported retailer product page
2. **Click**: "Start Price Selection" in extension
3. **Expected**: Page should show overlay with instructions
4. **Click**: On the price element on the page
5. **Expected**: Extension should capture the price

### **Scenario 4: Unsupported Website**
1. **Visit**: https://google.com
2. **Open**: Extension popup
3. **Expected**: Should show "This retailer is not supported yet"

## 🔍 Debugging Steps

### **Extension Console Logs**
1. Right-click extension icon → "Inspect popup"
2. Check Console tab for errors
3. Look for network requests to your API

### **Content Script Debugging**
1. On retailer page: F12 → Console
2. Look for "BargainHawk:" log messages
3. Check for JavaScript errors

### **Background Script Debugging**
1. Go to `chrome://extensions/`
2. Click "Inspect views: background page"
3. Check Console for service worker logs

## 🚨 Common Issues & Fixes

### **"Login Failed" Error**
- **Check**: API endpoint URL in `popup.js`
- **Verify**: Your scraper backend is running
- **Test**: Try API directly: `curl http://localhost:8080/productList?email=<EMAIL>`

### **"Network Error" on Login**
- **CORS Issue**: Your backend needs to allow Chrome extension origin
- **Add to SecurityConfig.java**:
```java
.allowedOriginPatterns("chrome-extension://*")
```

### **Auto-Detect Not Working**
- **Check**: Browser console on retailer page
- **Try**: Manual selection instead
- **Verify**: Retailer is in supported list

### **Products Not Adding**
- **5-Product Limit**: Check if user has reached limit
- **Authentication**: Verify Supabase token is valid
- **API Format**: Check request payload matches your backend

## 📋 Testing Checklist

### **Basic Functionality**
- [ ] Extension loads without errors
- [ ] Icons display correctly
- [ ] Popup opens and shows login form
- [ ] Can login with valid credentials
- [ ] Logout works properly
- [ ] Settings can be opened and saved

### **Retailer Detection**
- [ ] Costco.ca product pages detected
- [ ] Walmart.ca product pages detected  
- [ ] Wayfair.ca product pages detected
- [ ] Best Buy.ca product pages detected
- [ ] Unsupported sites show appropriate message

### **Product Detection**
- [ ] Auto-detect finds product name
- [ ] Auto-detect finds product price
- [ ] Auto-detect finds product image
- [ ] Manual selection mode works
- [ ] Price elements can be clicked and selected

### **API Integration**
- [ ] Products successfully added to BargainHawk
- [ ] Target prices are saved correctly
- [ ] 5-product limit is enforced
- [ ] Error messages are user-friendly
- [ ] Success notifications appear

### **Edge Cases**
- [ ] Works with products that have sale prices
- [ ] Handles products with no images
- [ ] Graceful failure when price not found
- [ ] Works with different URL formats
- [ ] Handles network timeouts

## 🔧 Development Testing

### **Hot Reload During Development**
```bash
npm run dev
```
This watches for file changes and rebuilds automatically.

### **After Making Changes**
1. Save your files
2. Go to `chrome://extensions/`
3. Click refresh icon on your extension
4. Test your changes

### **Validation**
```bash
npm run validate
```
Checks for common extension issues.

## 🚀 Production Testing

### **Before Deployment**
1. Update API URLs to production
2. Test with production BargainHawk API
3. Verify all retailers work in production
4. Test with real user accounts
5. Check error handling with production data

### **Performance Testing**
- Test on slow networks
- Test with large product catalogs
- Verify memory usage is reasonable
- Check for memory leaks during extended use

## 📞 Getting Help

### **Extension Not Working?**
1. Check all console logs (popup, content script, background)
2. Verify API endpoints are correct
3. Test API directly with curl/Postman
4. Check Chrome extension permissions

### **API Integration Issues?**
1. Verify Supabase authentication is working
2. Check CORS configuration in backend
3. Test with different user accounts
4. Verify request/response formats match

### **Need to Debug Further?**
1. Add more console.log statements
2. Use Chrome DevTools Network tab
3. Test individual components separately
4. Create minimal test cases
