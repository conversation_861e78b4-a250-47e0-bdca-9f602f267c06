// BargainHawk Chrome Extension - Popup Logic
class BargainHawkPopup {
  constructor() {
    this.currentUser = null;
    this.currentTab = null;
    this.productData = null;
    // Use your actual scraper API URL - update this for production
    this.apiBaseUrl = 'http://localhost:8080'; // Development
    // this.apiBaseUrl = 'https://your-scraper-app.fly.dev'; // Production

    this.init();
  }
  
  async init() {
    // Get current tab
    this.currentTab = await this.getCurrentTab();
    
    // Check authentication status
    await this.checkAuthStatus();
    
    // Setup event listeners
    this.setupEventListeners();
    
    // Initialize UI based on auth status
    this.initializeUI();
  }
  
  async getCurrentTab() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    return tab;
  }
  
  async checkAuthStatus() {
    try {
      const result = await chrome.storage.local.get(['authToken', 'user']);
      if (result.authToken && result.user) {
        this.currentUser = result.user;
        return true;
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    }
    return false;
  }
  
  setupEventListeners() {
    try {
      // Login form
      const loginForm = document.getElementById('login-form');
      if (loginForm) {
        loginForm.addEventListener('submit', this.handleLogin.bind(this));
      }

      // Logout
      const logoutBtn = document.getElementById('logout-btn');
      if (logoutBtn) {
        logoutBtn.addEventListener('click', this.handleLogout.bind(this));
      }

      // Product actions
      const startSelectionBtn = document.getElementById('start-selection-btn');
      if (startSelectionBtn) {
        startSelectionBtn.addEventListener('click', this.startPriceSelection.bind(this));
      }

      const autoDetectBtn = document.getElementById('auto-detect-btn');
      if (autoDetectBtn) {
        autoDetectBtn.addEventListener('click', this.autoDetectProduct.bind(this));
      }

      const addProductBtn = document.getElementById('add-product-btn');
      if (addProductBtn) {
        addProductBtn.addEventListener('click', this.addProduct.bind(this));
      }

      // Navigation
      const viewDashboardBtn = document.getElementById('view-dashboard-btn');
      if (viewDashboardBtn) {
        viewDashboardBtn.addEventListener('click', this.openDashboard.bind(this));
      }

      const settingsBtn = document.getElementById('settings-btn');
      if (settingsBtn) {
        settingsBtn.addEventListener('click', this.showSettings.bind(this));
      }

      const backBtn = document.getElementById('back-btn');
      if (backBtn) {
        backBtn.addEventListener('click', this.showMainScreen.bind(this));
      }

      // Settings
      const clearDataBtn = document.getElementById('clear-data-btn');
      if (clearDataBtn) {
        clearDataBtn.addEventListener('click', this.clearExtensionData.bind(this));
      }

      console.log('Event listeners setup complete');
    } catch (error) {
      console.error('Error setting up event listeners:', error);
    }
  }
  
  async initializeUI() {
    this.hideAllScreens();
    
    if (this.currentUser) {
      await this.showMainScreen();
    } else {
      this.showScreen('login-screen');
    }
  }
  
  hideAllScreens() {
    document.querySelectorAll('.screen').forEach(screen => {
      screen.classList.add('hidden');
    });
  }
  
  showScreen(screenId) {
    this.hideAllScreens();
    document.getElementById(screenId).classList.remove('hidden');
  }
  
  async showMainScreen() {
    this.showScreen('main-screen');
    
    // Update user info
    if (this.currentUser) {
      document.getElementById('user-name').textContent = `Hi, ${this.currentUser.firstName || this.currentUser.email}!`;
    }
    
    // Check if we're on a supported retailer page
    await this.checkRetailerPage();
  }
  
  async checkRetailerPage() {
    try {
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'getRetailerInfo'
      });
      
      if (response && response.success) {
        document.getElementById('retailer-name').textContent = response.retailer;
        document.getElementById('page-status').textContent = response.isProductPage ? 
          'Product page detected' : 'Browse to a product page';
        
        if (response.isProductPage) {
          document.getElementById('price-selection').classList.remove('hidden');
        }
      } else {
        document.getElementById('retailer-name').textContent = 'Unsupported website';
        document.getElementById('page-status').textContent = 'Visit a supported retailer';
      }
    } catch (error) {
      console.error('Error checking retailer page:', error);
      document.getElementById('retailer-name').textContent = 'Unable to detect retailer';
      document.getElementById('page-status').textContent = 'Refresh the page and try again';
    }
  }
  
  async handleLogin(event) {
    event.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const loginBtn = document.getElementById('login-btn');
    
    loginBtn.textContent = 'Signing in...';
    loginBtn.disabled = true;
    
    try {
      const response = await fetch(`${this.apiBaseUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
      });
      
      const data = await response.json();
      
      if (response.ok && data.token) {
        // Save auth data
        await chrome.storage.local.set({
          authToken: data.token,
          user: data.user
        });
        
        this.currentUser = data.user;
        await this.showMainScreen();
      } else {
        this.showError(data.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      this.showError('Network error. Please try again.');
    } finally {
      loginBtn.textContent = 'Sign In';
      loginBtn.disabled = false;
    }
  }
  
  async handleLogout() {
    await chrome.storage.local.remove(['authToken', 'user']);
    this.currentUser = null;
    this.showScreen('login-screen');
  }
  
  async startPriceSelection() {
    try {
      await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'startPriceSelection'
      });
      
      // Close popup to let user interact with page
      window.close();
    } catch (error) {
      console.error('Error starting price selection:', error);
      this.showError('Unable to start price selection');
    }
  }
  
  async autoDetectProduct() {
    const detectBtn = document.getElementById('auto-detect-btn');
    detectBtn.textContent = 'Detecting...';
    detectBtn.disabled = true;
    
    try {
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'extractProduct'
      });
      
      if (response && response.success && response.data) {
        this.productData = response.data;
        this.showProductPreview(response.data);
      } else {
        this.showError('Unable to detect product. Try manual price selection.');
      }
    } catch (error) {
      console.error('Error auto-detecting product:', error);
      this.showError('Unable to detect product. Try manual price selection.');
    } finally {
      detectBtn.textContent = 'Auto-Detect Product';
      detectBtn.disabled = false;
    }
  }
  
  showProductPreview(productData) {
    // Populate product preview
    document.getElementById('product-name').textContent = productData.name || 'Unknown Product';
    document.getElementById('product-price').textContent = productData.price?.toFixed(2) || '0.00';
    document.getElementById('product-retailer').textContent = productData.retailer || 'Unknown Retailer';
    
    if (productData.image) {
      document.getElementById('product-image').src = productData.image;
      document.getElementById('product-image').style.display = 'block';
    } else {
      document.getElementById('product-image').style.display = 'none';
    }
    
    // Show product preview section
    document.getElementById('product-preview').classList.remove('hidden');
    document.getElementById('price-selection').classList.add('hidden');
  }
  
  async addProduct() {
    if (!this.productData) {
      this.showError('No product data available');
      return;
    }
    
    const addBtn = document.getElementById('add-product-btn');
    const targetPrice = document.getElementById('target-price').value;
    
    addBtn.textContent = 'Adding...';
    addBtn.disabled = true;
    
    try {
      const authToken = await this.getAuthToken();
      if (!authToken) {
        this.showError('Please log in again');
        return;
      }
      
      const productPayload = {
        ...this.productData,
        targetPrice: targetPrice ? parseFloat(targetPrice) : null,
        addedVia: 'chrome-extension'
      };
      
      const response = await fetch(`${this.apiBaseUrl}/products`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(productPayload)
      });
      
      const data = await response.json();
      
      if (response.ok) {
        this.showSuccess('Product added successfully!');
        
        // Reset form after success
        setTimeout(() => {
          document.getElementById('product-preview').classList.add('hidden');
          document.getElementById('price-selection').classList.remove('hidden');
          document.getElementById('target-price').value = '';
        }, 2000);
      } else {
        this.showError(data.message || 'Failed to add product');
      }
    } catch (error) {
      console.error('Error adding product:', error);
      this.showError('Network error. Please try again.');
    } finally {
      addBtn.textContent = 'Add to BargainHawk';
      addBtn.disabled = false;
    }
  }
  
  async getAuthToken() {
    const result = await chrome.storage.local.get(['authToken']);
    return result.authToken;
  }
  
  openDashboard() {
    chrome.tabs.create({ url: 'https://bargainhawk.ca/dashboard' });
  }
  
  showSettings() {
    this.showScreen('settings-screen');
    
    // Load current settings
    this.loadSettings();
  }
  
  async loadSettings() {
    const settings = await chrome.storage.local.get([
      'costco-enabled', 'walmart-enabled', 'wayfair-enabled', 'bestbuy-enabled',
      'price-drop-notifications', 'success-notifications',
      'auto-detect-products', 'show-floating-button'
    ]);
    
    // Update checkboxes based on stored settings
    Object.keys(settings).forEach(key => {
      const checkbox = document.getElementById(key);
      if (checkbox) {
        checkbox.checked = settings[key] !== false; // Default to true if not set
      }
    });
    
    // Update user email
    if (this.currentUser) {
      document.getElementById('settings-user-email').textContent = this.currentUser.email;
    }
  }
  
  async clearExtensionData() {
    if (confirm('This will clear all extension data including login information. Continue?')) {
      await chrome.storage.local.clear();
      this.currentUser = null;
      this.showScreen('login-screen');
    }
  }
  
  showSuccess(message) {
    const successEl = document.getElementById('success-message');
    const messageArea = document.getElementById('message-area');
    
    successEl.querySelector('.message-text').textContent = message;
    messageArea.classList.remove('hidden');
    successEl.classList.remove('hidden');
    
    setTimeout(() => {
      successEl.classList.add('hidden');
      messageArea.classList.add('hidden');
    }, 3000);
  }
  
  showError(message) {
    const errorEl = document.getElementById('error-message');
    const messageArea = document.getElementById('message-area');
    
    errorEl.querySelector('.message-text').textContent = message;
    messageArea.classList.remove('hidden');
    errorEl.classList.remove('hidden');
    
    setTimeout(() => {
      errorEl.classList.add('hidden');
      messageArea.classList.add('hidden');
    }, 5000);
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  try {
    console.log('DOM loaded, initializing popup...');
    new BargainHawkPopup();
  } catch (error) {
    console.error('Error initializing popup:', error);
  }
});
