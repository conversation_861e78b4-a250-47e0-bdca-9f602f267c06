// Extension testing framework
const fs = require('fs');
const path = require('path');

class ExtensionTester {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
  }
  
  async runAllTests() {
    console.log('🧪 Running BargainHawk Extension Tests...\n');
    
    // Register all tests
    this.registerTests();
    
    // Run tests
    for (const test of this.tests) {
      await this.runTest(test);
    }
    
    this.printSummary();
    
    return this.failed === 0;
  }
  
  registerTests() {
    // Manifest tests
    this.addTest('Manifest Structure', this.testManifestStructure.bind(this));
    this.addTest('Required Permissions', this.testRequiredPermissions.bind(this));
    this.addTest('Content Script Configuration', this.testContentScriptConfig.bind(this));
    
    // File tests
    this.addTest('Required Files Exist', this.testRequiredFiles.bind(this));
    this.addTest('JavaScript Syntax', this.testJavaScriptSyntax.bind(this));
    this.addTest('CSS Syntax', this.testCSSSyntax.bind(this));
    
    // Functionality tests
    this.addTest('Retailer Detection Logic', this.testRetailerDetection.bind(this));
    this.addTest('Price Selector Generation', this.testPriceSelectorGeneration.bind(this));
    this.addTest('API Integration Points', this.testAPIIntegration.bind(this));
    
    // Security tests
    this.addTest('No Unsafe Practices', this.testSecurityPractices.bind(this));
    this.addTest('CSP Compliance', this.testCSPCompliance.bind(this));
  }
  
  addTest(name, testFunction) {
    this.tests.push({ name, testFunction });
  }
  
  async runTest(test) {
    try {
      const result = await test.testFunction();
      if (result.success) {
        console.log(`✅ ${test.name}`);
        if (result.details) {
          console.log(`   ${result.details}`);
        }
        this.passed++;
      } else {
        console.log(`❌ ${test.name}`);
        console.log(`   ${result.error}`);
        this.failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name}`);
      console.log(`   Unexpected error: ${error.message}`);
      this.failed++;
    }
  }
  
  testManifestStructure() {
    if (!fs.existsSync('manifest.json')) {
      return { success: false, error: 'manifest.json not found' };
    }
    
    try {
      const manifest = JSON.parse(fs.readFileSync('manifest.json', 'utf8'));
      
      const required = ['manifest_version', 'name', 'version', 'description'];
      for (const field of required) {
        if (!manifest[field]) {
          return { success: false, error: `Missing required field: ${field}` };
        }
      }
      
      if (manifest.manifest_version !== 3) {
        return { success: false, error: 'Should use Manifest V3' };
      }
      
      return { 
        success: true, 
        details: `v${manifest.version} - ${manifest.name}` 
      };
    } catch (error) {
      return { success: false, error: `Invalid JSON: ${error.message}` };
    }
  }
  
  testRequiredPermissions() {
    try {
      const manifest = JSON.parse(fs.readFileSync('manifest.json', 'utf8'));
      
      const requiredPermissions = ['activeTab', 'storage', 'scripting'];
      const permissions = manifest.permissions || [];
      
      for (const perm of requiredPermissions) {
        if (!permissions.includes(perm)) {
          return { success: false, error: `Missing permission: ${perm}` };
        }
      }
      
      // Check host permissions for supported retailers
      const hostPermissions = manifest.host_permissions || [];
      const requiredHosts = ['costco.ca', 'walmart.ca', 'wayfair.ca'];
      
      for (const host of requiredHosts) {
        const hasHost = hostPermissions.some(hp => hp.includes(host));
        if (!hasHost) {
          return { success: false, error: `Missing host permission for: ${host}` };
        }
      }
      
      return { 
        success: true, 
        details: `${permissions.length} permissions, ${hostPermissions.length} host permissions` 
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  testContentScriptConfig() {
    try {
      const manifest = JSON.parse(fs.readFileSync('manifest.json', 'utf8'));
      
      if (!manifest.content_scripts || manifest.content_scripts.length === 0) {
        return { success: false, error: 'No content scripts configured' };
      }
      
      const contentScript = manifest.content_scripts[0];
      
      if (!contentScript.matches || contentScript.matches.length === 0) {
        return { success: false, error: 'Content script has no URL matches' };
      }
      
      if (!contentScript.js || !contentScript.js.includes('content-script.js')) {
        return { success: false, error: 'Content script JS file not specified' };
      }
      
      return { 
        success: true, 
        details: `${contentScript.matches.length} URL patterns matched` 
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  testRequiredFiles() {
    const requiredFiles = [
      'manifest.json',
      'background.js',
      'content-script.js',
      'popup.html',
      'popup.js',
      'popup.css',
      'content-styles.css'
    ];
    
    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        return { success: false, error: `Missing file: ${file}` };
      }
    }
    
    return { 
      success: true, 
      details: `All ${requiredFiles.length} required files present` 
    };
  }
  
  testJavaScriptSyntax() {
    const jsFiles = ['background.js', 'content-script.js', 'popup.js'];
    
    for (const file of jsFiles) {
      if (fs.existsSync(file)) {
        try {
          const content = fs.readFileSync(file, 'utf8');
          
          // Basic syntax checks
          if (content.includes('eval(')) {
            return { success: false, error: `${file} contains eval() which is not allowed` };
          }
          
          // Check for proper class/function structure
          if (file === 'content-script.js' && !content.includes('class')) {
            return { success: false, error: `${file} should use class structure` };
          }
          
        } catch (error) {
          return { success: false, error: `Error reading ${file}: ${error.message}` };
        }
      }
    }
    
    return { success: true, details: 'JavaScript syntax checks passed' };
  }
  
  testCSSSyntax() {
    const cssFiles = ['popup.css', 'content-styles.css'];
    
    for (const file of cssFiles) {
      if (fs.existsSync(file)) {
        try {
          const content = fs.readFileSync(file, 'utf8');
          
          // Basic CSS validation
          const openBraces = (content.match(/{/g) || []).length;
          const closeBraces = (content.match(/}/g) || []).length;
          
          if (openBraces !== closeBraces) {
            return { success: false, error: `${file} has mismatched braces` };
          }
          
        } catch (error) {
          return { success: false, error: `Error reading ${file}: ${error.message}` };
        }
      }
    }
    
    return { success: true, details: 'CSS syntax checks passed' };
  }
  
  testRetailerDetection() {
    if (!fs.existsSync('content-script.js')) {
      return { success: false, error: 'content-script.js not found' };
    }
    
    const content = fs.readFileSync('content-script.js', 'utf8');
    
    // Check for retailer detection logic
    const retailers = ['costco', 'walmart', 'wayfair', 'bestbuy'];
    
    for (const retailer of retailers) {
      if (!content.includes(retailer)) {
        return { success: false, error: `Missing retailer configuration: ${retailer}` };
      }
    }
    
    // Check for price selector arrays
    if (!content.includes('priceSelectors')) {
      return { success: false, error: 'Missing price selector configuration' };
    }
    
    return { 
      success: true, 
      details: `${retailers.length} retailers configured with price selectors` 
    };
  }
  
  testPriceSelectorGeneration() {
    if (!fs.existsSync('content-script.js')) {
      return { success: false, error: 'content-script.js not found' };
    }
    
    const content = fs.readFileSync('content-script.js', 'utf8');
    
    // Check for selector generation methods
    const requiredMethods = ['generateSelectors', 'getCSSPath', 'isPriceElement'];
    
    for (const method of requiredMethods) {
      if (!content.includes(method)) {
        return { success: false, error: `Missing method: ${method}` };
      }
    }
    
    return { success: true, details: 'Price selector generation methods present' };
  }
  
  testAPIIntegration() {
    const files = ['popup.js', 'background.js'];
    
    for (const file of files) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        
        // Check for API endpoint configuration
        if (!content.includes('api.bargainhawk.ca') && !content.includes('apiBaseUrl')) {
          return { success: false, error: `${file} missing API endpoint configuration` };
        }
        
        // Check for authentication handling
        if (!content.includes('Authorization') && !content.includes('authToken')) {
          return { success: false, error: `${file} missing authentication handling` };
        }
      }
    }
    
    return { success: true, details: 'API integration points configured' };
  }
  
  testSecurityPractices() {
    const jsFiles = ['background.js', 'content-script.js', 'popup.js'];
    
    for (const file of jsFiles) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        
        // Check for unsafe practices
        const unsafePractices = ['eval(', 'innerHTML =', 'document.write'];
        
        for (const practice of unsafePractices) {
          if (content.includes(practice)) {
            return { success: false, error: `${file} contains unsafe practice: ${practice}` };
          }
        }
        
        // Check for HTTP URLs (should use HTTPS)
        if (content.includes('http://') && !content.includes('localhost')) {
          return { success: false, error: `${file} contains insecure HTTP URLs` };
        }
      }
    }
    
    return { success: true, details: 'No unsafe security practices detected' };
  }
  
  testCSPCompliance() {
    if (!fs.existsSync('popup.html')) {
      return { success: false, error: 'popup.html not found' };
    }
    
    const content = fs.readFileSync('popup.html', 'utf8');
    
    // Check for inline scripts (not allowed with CSP)
    if (content.includes('<script>') && !content.includes('src=')) {
      return { success: false, error: 'popup.html contains inline scripts' };
    }
    
    // Check for inline styles (should be in external CSS)
    if (content.includes('style=')) {
      return { success: false, error: 'popup.html contains inline styles' };
    }
    
    return { success: true, details: 'CSP compliance checks passed' };
  }
  
  printSummary() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(50));
    
    const total = this.passed + this.failed;
    console.log(`Total tests: ${total}`);
    console.log(`✅ Passed: ${this.passed}`);
    console.log(`❌ Failed: ${this.failed}`);
    
    if (this.failed === 0) {
      console.log('\n🎉 All tests passed! Extension is ready for deployment.');
    } else {
      console.log(`\n⚠️  ${this.failed} test(s) failed. Please fix issues before deployment.`);
    }
    
    console.log('='.repeat(50));
  }
}

// Run tests
const tester = new ExtensionTester();
tester.runAllTests().then(success => {
  process.exit(success ? 0 : 1);
});
