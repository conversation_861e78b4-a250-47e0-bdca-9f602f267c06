// BargainHawk Chrome Extension - Content Script
// Handles price detection and product extraction on retailer websites

class BargainHawkContentScript {
  constructor() {
    this.isActive = false;
    this.selectedPriceElement = null;
    this.retailerConfig = null;
    this.overlay = null;
    
    this.init();
  }
  
  init() {
    // Detect current retailer
    this.retailerConfig = this.detectRetailer();
    
    if (this.retailerConfig) {
      console.log('BargainHawk: Detected retailer:', this.retailerConfig.name);
      this.setupExtension();
    }
  }
  
  detectRetailer() {
    const hostname = window.location.hostname.toLowerCase();
    const pathname = window.location.pathname;
    
    const retailers = {
      costco: {
        name: 'Costco Canada',
        domains: ['costco.ca', 'www.costco.ca'],
        priceSelectors: [
          '.price-current',
          '.price .sr-only',
          '[data-automation-id="product-price"]',
          '.product-price-value',
          '.price-range'
        ],
        productNameSelectors: [
          'h1[data-automation-id="product-title"]',
          '.product-h1',
          'h1.product-title'
        ],
        imageSelectors: [
          '.product-image-main img',
          '.carousel-item img',
          '.product-images img'
        ],
        productPagePattern: /\/p\//
      },
      
      walmart: {
        name: 'Walmart Canada',
        domains: ['walmart.ca', 'www.walmart.ca'],
        priceSelectors: [
          '[data-automation-id="product-price"]',
          '.price-current',
          '.price-group .price',
          '[itemprop="price"]'
        ],
        productNameSelectors: [
          'h1[data-automation-id="product-title"]',
          '.product-title h1',
          'h1.product-name'
        ],
        imageSelectors: [
          '.product-image img',
          '.hero-image img',
          '.carousel-slide img'
        ],
        productPagePattern: /\/ip\//
      },
      
      wayfair: {
        name: 'Wayfair Canada',
        domains: ['wayfair.ca', 'www.wayfair.ca'],
        priceSelectors: [
          '[data-enzyme-id="PriceBlock"]',
          '.BasePriceBlock',
          '.ProductDetailsPriceBlock',
          '.price'
        ],
        productNameSelectors: [
          'h1[data-enzyme-id="ProductTitle"]',
          '.product-title h1',
          'h1.ProductTitle'
        ],
        imageSelectors: [
          '.ProductImageCarousel img',
          '.product-image img',
          '.hero-image img'
        ],
        productPagePattern: /\/pdp\//
      },
      
      bestbuy: {
        name: 'Best Buy Canada',
        domains: ['bestbuy.ca', 'www.bestbuy.ca'],
        priceSelectors: [
          '.sr-only:contains("current price")',
          '.pricing_price',
          '.price-current',
          '[data-testid="product-price"]'
        ],
        productNameSelectors: [
          '.product-title',
          'h1.productName',
          '.pdp-product-name h1'
        ],
        imageSelectors: [
          '.product-image img',
          '.hero-image img',
          '.primary-image img'
        ],
        productPagePattern: /\/product\//
      }
    };
    
    for (const [key, config] of Object.entries(retailers)) {
      if (config.domains.some(domain => hostname.includes(domain))) {
        // Check if we're on a product page
        if (config.productPagePattern.test(pathname)) {
          return { ...config, key };
        }
      }
    }
    
    return null;
  }
  
  setupExtension() {
    // Create floating action button
    this.createFloatingButton();
    
    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'startPriceSelection') {
        this.startPriceSelection();
        sendResponse({ success: true });
      } else if (request.action === 'extractProduct') {
        const productData = this.extractProductData();
        sendResponse({ success: true, data: productData });
      } else if (request.action === 'getRetailerInfo') {
        sendResponse({ 
          success: true, 
          retailer: this.retailerConfig.name,
          isProductPage: true 
        });
      }
    });
  }
  
  createFloatingButton() {
    const button = document.createElement('div');
    button.id = 'bargainhawk-float-btn';
    button.innerHTML = `
      <div class="bh-float-button">
        <img src="${chrome.runtime.getURL('icons/icon32.png')}" alt="BargainHawk" />
        <span>Track Price</span>
      </div>
    `;
    
    button.addEventListener('click', () => {
      this.openExtensionPopup();
    });
    
    document.body.appendChild(button);
  }
  
  startPriceSelection() {
    this.isActive = true;
    this.createOverlay();
    
    // Add click listeners to detect price elements
    document.addEventListener('click', this.handlePriceSelection.bind(this), true);
    document.addEventListener('mouseover', this.highlightElement.bind(this));
    document.addEventListener('mouseout', this.removeHighlight.bind(this));
  }
  
  createOverlay() {
    this.overlay = document.createElement('div');
    this.overlay.id = 'bargainhawk-overlay';
    this.overlay.innerHTML = `
      <div class="bh-overlay-content">
        <h3>Select Price Element</h3>
        <p>Click on the price to teach BargainHawk where to find it</p>
        <button id="bh-cancel-selection">Cancel</button>
      </div>
    `;
    
    document.body.appendChild(this.overlay);
    
    document.getElementById('bh-cancel-selection').addEventListener('click', () => {
      this.stopPriceSelection();
    });
  }
  
  handlePriceSelection(event) {
    if (!this.isActive) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    const element = event.target;
    const text = element.textContent.trim();
    
    // Check if clicked element contains price-like text
    if (this.isPriceElement(text)) {
      this.selectedPriceElement = element;
      this.savePriceSelector(element);
      this.stopPriceSelection();
      this.showSuccessMessage();
    }
  }
  
  isPriceElement(text) {
    // Canadian price patterns
    const pricePatterns = [
      /\$[\d,]+\.?\d*/,  // $123.45, $1,234
      /CAD\s*\$?[\d,]+\.?\d*/,  // CAD $123.45
      /[\d,]+\.?\d*\s*CAD/,  // 123.45 CAD
    ];
    
    return pricePatterns.some(pattern => pattern.test(text));
  }
  
  savePriceSelector(element) {
    // Generate multiple selector strategies
    const selectors = this.generateSelectors(element);
    
    // Save to storage for this retailer
    const storageKey = `priceSelector_${this.retailerConfig.key}`;
    chrome.storage.local.set({
      [storageKey]: {
        selectors: selectors,
        timestamp: Date.now(),
        url: window.location.href
      }
    });
  }
  
  generateSelectors(element) {
    const selectors = [];
    
    // ID selector
    if (element.id) {
      selectors.push(`#${element.id}`);
    }
    
    // Class selector
    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.trim());
      if (classes.length > 0) {
        selectors.push(`.${classes.join('.')}`);
      }
    }
    
    // Data attribute selectors
    Array.from(element.attributes).forEach(attr => {
      if (attr.name.startsWith('data-')) {
        selectors.push(`[${attr.name}="${attr.value}"]`);
      }
    });
    
    // CSS path
    selectors.push(this.getCSSPath(element));
    
    return selectors;
  }
  
  getCSSPath(element) {
    const path = [];
    while (element && element.nodeType === Node.ELEMENT_NODE) {
      let selector = element.nodeName.toLowerCase();
      if (element.id) {
        selector += `#${element.id}`;
        path.unshift(selector);
        break;
      } else {
        let sibling = element;
        let nth = 1;
        while (sibling = sibling.previousElementSibling) {
          if (sibling.nodeName.toLowerCase() === selector) nth++;
        }
        if (nth !== 1) selector += `:nth-of-type(${nth})`;
      }
      path.unshift(selector);
      element = element.parentNode;
    }
    return path.join(' > ');
  }
  
  extractProductData() {
    const data = {
      url: window.location.href,
      retailer: this.retailerConfig.name,
      timestamp: Date.now()
    };
    
    // Extract product name
    data.name = this.extractBySelectors(this.retailerConfig.productNameSelectors);
    
    // Extract price using saved selector or default selectors
    data.price = this.extractPrice();
    
    // Extract image
    data.image = this.extractImage();
    
    // Extract additional metadata
    data.metadata = this.extractMetadata();
    
    return data;
  }
  
  extractBySelectors(selectors) {
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.textContent.trim();
      }
    }
    return null;
  }
  
  extractPrice() {
    // First try user-selected price element
    if (this.selectedPriceElement) {
      return this.parsePrice(this.selectedPriceElement.textContent);
    }
    
    // Try saved selectors for this retailer
    const storageKey = `priceSelector_${this.retailerConfig.key}`;
    chrome.storage.local.get([storageKey], (result) => {
      if (result[storageKey]) {
        for (const selector of result[storageKey].selectors) {
          const element = document.querySelector(selector);
          if (element) {
            return this.parsePrice(element.textContent);
          }
        }
      }
    });
    
    // Fall back to default selectors
    for (const selector of this.retailerConfig.priceSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        return this.parsePrice(element.textContent);
      }
    }
    
    return null;
  }
  
  parsePrice(text) {
    const match = text.match(/\$?([\d,]+\.?\d*)/);
    return match ? parseFloat(match[1].replace(/,/g, '')) : null;
  }
  
  extractImage() {
    for (const selector of this.retailerConfig.imageSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.src || element.getAttribute('data-src');
      }
    }
    return null;
  }
  
  extractMetadata() {
    return {
      title: document.title,
      description: document.querySelector('meta[name="description"]')?.content,
      brand: document.querySelector('[itemprop="brand"]')?.textContent,
      model: document.querySelector('[itemprop="model"]')?.textContent,
      sku: document.querySelector('[itemprop="sku"]')?.textContent
    };
  }
  
  highlightElement(event) {
    if (!this.isActive) return;
    
    const element = event.target;
    if (this.isPriceElement(element.textContent)) {
      element.style.outline = '2px solid #007bff';
      element.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
    }
  }
  
  removeHighlight(event) {
    if (!this.isActive) return;
    
    const element = event.target;
    element.style.outline = '';
    element.style.backgroundColor = '';
  }
  
  stopPriceSelection() {
    this.isActive = false;
    
    // Remove event listeners
    document.removeEventListener('click', this.handlePriceSelection, true);
    document.removeEventListener('mouseover', this.highlightElement);
    document.removeEventListener('mouseout', this.removeHighlight);
    
    // Remove overlay
    if (this.overlay) {
      this.overlay.remove();
      this.overlay = null;
    }
  }
  
  showSuccessMessage() {
    const message = document.createElement('div');
    message.id = 'bargainhawk-success';
    message.innerHTML = `
      <div class="bh-success-message">
        ✅ Price element saved! You can now track this product.
      </div>
    `;
    
    document.body.appendChild(message);
    
    setTimeout(() => {
      message.remove();
    }, 3000);
  }
  
  openExtensionPopup() {
    // This will be handled by the popup when user clicks the extension icon
    // We can send a message to background script to open popup
    chrome.runtime.sendMessage({ action: 'openPopup' });
  }
}

// Initialize the content script
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new BargainHawkContentScript();
  });
} else {
  new BargainHawkContentScript();
}
