#!/bin/bash

# BargainHawk Chrome Extension Build Script

echo "🔨 Building BargainHawk Chrome Extension..."

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf dist

# Create icons
echo "🎨 Creating icons..."
node create-icons.js

# Create PNG placeholders
echo "📷 Creating PNG placeholders..."
node -e "
const fs = require('fs');
const path = require('path');

const createPNGPlaceholder = () => {
  return Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==', 'base64');
};

const sizes = [16, 32, 48, 128];
const iconsDir = './icons';

sizes.forEach(size => {
  const pngFilename = path.join(iconsDir, \`icon\${size}.png\`);
  const pngData = createPNGPlaceholder();
  fs.writeFileSync(pngFilename, pngData);
  console.log(\`Created \${pngFilename}\`);
});
"

# Create dist directory and copy files
echo "📦 Building extension..."
mkdir -p dist
cp -r *.js *.html *.css *.json icons dist/ 2>/dev/null || true

# Remove unnecessary files from dist
rm -f dist/create-icons.js dist/build.sh dist/setup.sh 2>/dev/null || true

echo ""
echo "✅ Build complete!"
echo ""
echo "📋 Next steps:"
echo "1. Open Chrome → chrome://extensions/"
echo "2. Enable 'Developer mode'"
echo "3. Click 'Load unpacked'"
echo "4. Select the 'dist/' folder"
echo ""
echo "🧪 Test the extension on:"
echo "- https://www.costco.ca/kirkland-signature-organic-extra-virgin-olive-oil%2c-2-l.product.100334738.html"
echo "- https://www.walmart.ca/en/ip/great-value-peanut-butter-smooth-1-kg/6000016691823"
echo ""
