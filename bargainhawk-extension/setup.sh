#!/bin/bash

# BargainHawk Chrome Extension Setup Script

echo "🚀 Setting up BargainHawk Chrome Extension..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ first."
    echo "   Download from: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js found: $(node --version)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Generate icons
echo "🎨 Generating extension icons..."
node create-icons.js

# Build extension
echo "🔨 Building extension..."
npm run build

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Open Chrome and go to: chrome://extensions/"
echo "2. Enable 'Developer mode' (toggle in top right)"
echo "3. Click 'Load unpacked'"
echo "4. Select the 'dist/' folder from this directory"
echo ""
echo "🧪 Testing:"
echo "1. Make sure your BargainHawk scraper is running on localhost:8080"
echo "2. Visit a Costco, Walmart, or Wayfair product page"
echo "3. Click the BargainHawk extension icon"
echo "4. Login and try adding a product"
echo ""
echo "📖 For detailed testing instructions, see TESTING.md"
echo ""
echo "🔧 Development commands:"
echo "  npm run build    - Build extension"
echo "  npm run dev      - Watch for changes"
echo "  npm run validate - Check for issues"
echo ""
