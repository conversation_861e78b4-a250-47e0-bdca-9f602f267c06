# BargainHawk Chrome Extension

A Chrome extension that allows users to track prices directly from retailer websites (Costco, Walmart, Wayfair, etc.) without copying URLs manually.

## 🚀 Features

- **Smart Price Detection**: Automatically detect product prices on supported retailer websites
- **Manual Price Selection**: Click on price elements to teach the extension where to find prices
- **Multi-Retailer Support**: Works with Costco Canada, Walmart Canada, Wayfair Canada, Best Buy Canada
- **BargainHawk Integration**: Seamlessly adds products to your BargainHawk account
- **Target Price Setting**: Set custom target prices for price drop alerts
- **Visual Feedback**: Floating action button and success notifications

## 🛠️ Development Setup

### Prerequisites
- Node.js 16+ 
- Chrome browser
- BargainHawk account

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd bargainhawk-extension
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Generate icons and build**
   ```bash
   npm run build
   ```

4. **Load extension in Chrome**
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top right)
   - Click "Load unpacked"
   - Select the `dist/` folder from this project

## 🧪 Testing Locally

### 1. Configure API Endpoint
Update the API URL in `popup.js`:
```javascript
// For local development
this.apiBaseUrl = 'http://localhost:8080';

// For production
// this.apiBaseUrl = 'https://your-scraper-app.fly.dev';
```

### 2. Test on Supported Retailers
Visit any of these websites with a product page:
- https://costco.ca (any product page)
- https://walmart.ca (any product page) 
- https://wayfair.ca (any product page)
- https://bestbuy.ca (any product page)

### 3. Extension Testing Flow
1. **Install Extension**: Load unpacked extension in Chrome
2. **Visit Retailer**: Go to a product page on supported retailer
3. **Open Extension**: Click the BargainHawk icon in Chrome toolbar
4. **Login**: Sign in with your BargainHawk account
5. **Detect Product**: Try "Auto-Detect Product" or "Start Price Selection"
6. **Add Product**: Set optional target price and add to BargainHawk

## 📋 Testing Checklist

### Basic Functionality
- [ ] Extension loads without errors
- [ ] Login form appears for unauthenticated users
- [ ] Can login with valid BargainHawk credentials
- [ ] Retailer detection works on supported sites
- [ ] Floating action button appears on product pages
- [ ] Auto-detect finds product name and price
- [ ] Manual price selection highlights elements
- [ ] Products are successfully added to BargainHawk
- [ ] Success notifications appear
- [ ] Settings panel opens and saves preferences

### Retailer-Specific Tests
- [ ] **Costco.ca**: Product detection on item pages
- [ ] **Walmart.ca**: Price extraction from product pages  
- [ ] **Wayfair.ca**: Product information parsing
- [ ] **Best Buy.ca**: Electronics product handling

### Error Handling
- [ ] Graceful handling of network errors
- [ ] Proper error messages for invalid login
- [ ] Fallback when auto-detection fails
- [ ] 5-product limit enforcement
- [ ] Unsupported website messaging

## 🔧 Development Commands

```bash
# Build extension for development
npm run build

# Run validation tests
npm run validate

# Run extension tests
npm run test

# Watch for changes during development
npm run dev

# Package for Chrome Web Store
npm run package
```

## 📁 Project Structure

```
bargainhawk-extension/
├── manifest.json          # Extension manifest
├── background.js          # Service worker
├── content-script.js      # Injected into retailer pages
├── popup.html            # Extension popup UI
├── popup.js              # Popup logic
├── popup.css             # Popup styles
├── content-styles.css    # Styles for retailer pages
├── icons/                # Extension icons
├── dist/                 # Built extension files
└── package.json          # Node.js dependencies
```

## 🐛 Troubleshooting

### Extension Won't Load
- Check `chrome://extensions/` for error messages
- Ensure all required files are present in `dist/` folder
- Run `npm run validate` to check for issues

### Login Issues
- Verify API endpoint URL is correct
- Check network tab for authentication errors
- Ensure BargainHawk backend is running

### Price Detection Not Working
- Try manual price selection mode
- Check browser console for JavaScript errors
- Verify retailer is in supported list

### Products Not Adding
- Check if 5-product limit is reached
- Verify authentication token is valid
- Check network requests in browser dev tools

## 🚀 Deployment

### Chrome Web Store
1. Run `npm run package` to create ZIP file
2. Go to [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)
3. Upload the generated ZIP file
4. Fill out store listing details
5. Submit for review

### Production Configuration
- Update API URLs to production endpoints
- Add proper error tracking
- Configure analytics if needed
- Test thoroughly on production API

## 📞 Support

For issues or questions:
- Check the troubleshooting section above
- Review browser console for error messages
- Test with different retailer websites
- Verify BargainHawk account is working

## 🔒 Privacy & Security

- Extension only accesses supported retailer domains
- Authentication tokens stored securely in Chrome storage
- No data collection beyond necessary product information
- All API communication uses HTTPS in production
