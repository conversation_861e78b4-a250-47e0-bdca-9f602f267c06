// BargainHawk Extension Configuration
// Update these settings for your environment

const CONFIG = {
  // API Configuration
  API: {
    // Local development (when running scraper on localhost)
    LOCAL: 'http://localhost:8080',
    
    // Production (update with your actual Fly.io URL)
    PRODUCTION: 'https://your-scraper-app.fly.dev',
    
    // Current environment - change this to switch between local/production
    CURRENT: 'LOCAL' // Change to 'PRODUCTION' for production testing
  },
  
  // Supported Retailers
  RETAILERS: {
    COSTCO: {
      enabled: true,
      domains: ['costco.ca', 'www.costco.ca'],
      name: 'Costco Canada'
    },
    WALMART: {
      enabled: true,
      domains: ['walmart.ca', 'www.walmart.ca'],
      name: 'Walmart Canada'
    },
    WAYFAIR: {
      enabled: true,
      domains: ['wayfair.ca', 'www.wayfair.ca'],
      name: 'Wayfair Canada'
    },
    BESTBUY: {
      enabled: true,
      domains: ['bestbuy.ca', 'www.bestbuy.ca'],
      name: 'Best Buy Canada'
    }
  },
  
  // Extension Settings
  EXTENSION: {
    // Default province for product tracking
    DEFAULT_PROVINCE: 'ON',
    
    // Auto-detect timeout (milliseconds)
    DETECTION_TIMEOUT: 5000,
    
    // Show debug logs in console
    DEBUG: true,
    
    // Default target price percentage (10% below current price)
    DEFAULT_TARGET_PERCENTAGE: 0.9
  }
};

// Helper function to get current API URL
function getApiUrl() {
  return CONFIG.API[CONFIG.API.CURRENT];
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CONFIG;
}
