// BargainHawk Chrome Extension - Background Script (Service Worker)
// Simplified version for Manifest V3 compatibility

console.log('BargainHawk background script loaded');

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  console.log('BargainHawk extension installed/updated:', details.reason);
  
  if (details.reason === 'install') {
    // Set default settings
    chrome.storage.local.set({
      'notifications': true,
      'autoDetect': true,
      'retailers': {
        'costco': true,
        'walmart': true,
        'wayfair': true,
        'bestbuy': true
      }
    });
    
    console.log('Default settings configured');
  }
});

// Handle messages from content scripts and popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Background received message:', message);
  
  switch (message.action) {
    case 'productDetected':
      handleProductDetected(message.data, sender);
      break;
    case 'showNotification':
      console.log('Notification request:', message.title, message.message);
      break;
    case 'openTab':
      chrome.tabs.create({ url: message.url });
      break;
    default:
      console.log('Unknown message action:', message.action);
  }
  
  // Return true to indicate we'll send a response asynchronously
  return true;
});

// Helper functions
function handleProductDetected(productData, sender) {
  console.log('Product detected:', productData);
  
  // Store detected product data
  chrome.storage.local.set({
    'lastDetectedProduct': {
      ...productData,
      tabId: sender.tab.id,
      timestamp: Date.now()
    }
  });
}

// Handle tab updates to detect supported retailers
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    const supportedDomains = [
      'costco.ca', 'walmart.ca', 'wayfair.ca', 'bestbuy.ca', 
      'canadiantire.ca', 'amazon.ca'
    ];
    
    const isSupported = supportedDomains.some(domain => tab.url.includes(domain));
    
    if (isSupported) {
      console.log('Supported retailer detected:', tab.url);
    }
  }
});

console.log('BargainHawk background script ready');
