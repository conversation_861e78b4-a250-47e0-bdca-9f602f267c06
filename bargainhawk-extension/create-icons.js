// Script to generate BargainHawk extension icons
// Run with: node create-icons.js

const fs = require('fs');
const path = require('path');

// Create icons directory
const iconsDir = path.join(__dirname, 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir);
}

// SVG icon template for BargainHawk
const createSVGIcon = (size) => `
<svg width="${size}" height="${size}" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="24" cy="24" r="22" fill="url(#gradient)" stroke="#1e40af" stroke-width="2"/>
  
  <!-- Price tag shape -->
  <path d="M12 16 L28 16 L32 24 L28 32 L12 32 Z" fill="white" opacity="0.9"/>
  
  <!-- Dollar sign -->
  <text x="20" y="28" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1e40af">$</text>
  
  <!-- Hawk wing/arrow indicating tracking -->
  <path d="M34 18 L38 22 L34 26 L36 24 Z" fill="white" opacity="0.8"/>
  
  <!-- Small dots indicating monitoring -->
  <circle cx="14" cy="20" r="1" fill="#1e40af"/>
  <circle cx="14" cy="24" r="1" fill="#1e40af"/>
  <circle cx="14" cy="28" r="1" fill="#1e40af"/>
</svg>
`;

// Create different sized icons
const sizes = [16, 32, 48, 128];

sizes.forEach(size => {
  const svgContent = createSVGIcon(size);
  const filename = path.join(iconsDir, `icon${size}.svg`);
  fs.writeFileSync(filename, svgContent.trim());
  console.log(`Created ${filename}`);
});

// Create a simple HTML file to preview icons
const previewHTML = `
<!DOCTYPE html>
<html>
<head>
  <title>BargainHawk Extension Icons Preview</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .icon-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; }
    .icon-item { background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .icon-item img { margin-bottom: 10px; }
    .dark-bg { background: #333; }
    .dark-bg img { background: #333; padding: 10px; border-radius: 4px; }
  </style>
</head>
<body>
  <h1>BargainHawk Extension Icons</h1>
  <div class="icon-grid">
    ${sizes.map(size => `
      <div class="icon-item">
        <img src="icon${size}.svg" alt="${size}x${size}" width="${size}" height="${size}">
        <div>${size}x${size}px</div>
      </div>
    `).join('')}
  </div>
  
  <h2>Dark Background Preview</h2>
  <div class="icon-grid">
    ${sizes.map(size => `
      <div class="icon-item dark-bg">
        <img src="icon${size}.svg" alt="${size}x${size}" width="${size}" height="${size}">
        <div style="color: white;">${size}x${size}px</div>
      </div>
    `).join('')}
  </div>
</body>
</html>
`;

fs.writeFileSync(path.join(iconsDir, 'preview.html'), previewHTML);
console.log('Created icons/preview.html - Open this file to preview the icons');

// Create simple PNG placeholders (base64 encoded 1x1 pixel PNGs)
const createPNGPlaceholder = () => {
  // This is a 1x1 transparent PNG in base64
  return Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==', 'base64');
};

// Create PNG files as placeholders
sizes.forEach(size => {
  const pngFilename = path.join(iconsDir, `icon${size}.png`);
  const pngData = createPNGPlaceholder();
  fs.writeFileSync(pngFilename, pngData);
  console.log(`Created placeholder ${pngFilename}`);
});

console.log('\n📝 Icon files created:');
console.log('✅ SVG icons for preview');
console.log('✅ PNG placeholders for Chrome extension');
console.log('\n💡 For better icons:');
console.log('1. Open icons/preview.html to see SVG icons');
console.log('2. Use an online SVG to PNG converter');
console.log('3. Replace the placeholder PNG files with proper icons');
