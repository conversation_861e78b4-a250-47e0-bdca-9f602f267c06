// Extension validation script
const fs = require('fs');
const path = require('path');

class ExtensionValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.info = [];
  }
  
  validate() {
    console.log('🔍 Validating BargainHawk Chrome Extension...\n');
    
    this.validateManifest();
    this.validateFiles();
    this.validatePermissions();
    this.validateIcons();
    this.validateScripts();
    
    this.printResults();
    
    return this.errors.length === 0;
  }
  
  validateManifest() {
    console.log('📋 Validating manifest.json...');
    
    if (!fs.existsSync('manifest.json')) {
      this.errors.push('manifest.json not found');
      return;
    }
    
    try {
      const manifest = JSON.parse(fs.readFileSync('manifest.json', 'utf8'));
      
      // Required fields
      const required = ['manifest_version', 'name', 'version', 'description'];
      required.forEach(field => {
        if (!manifest[field]) {
          this.errors.push(`manifest.json missing required field: ${field}`);
        }
      });
      
      // Manifest version
      if (manifest.manifest_version !== 3) {
        this.warnings.push('Using Manifest V2 - consider upgrading to V3');
      }
      
      // Permissions
      if (!manifest.permissions || manifest.permissions.length === 0) {
        this.warnings.push('No permissions specified');
      }
      
      // Host permissions
      if (!manifest.host_permissions || manifest.host_permissions.length === 0) {
        this.warnings.push('No host permissions specified');
      }
      
      // Content scripts
      if (manifest.content_scripts) {
        manifest.content_scripts.forEach((script, index) => {
          if (!script.matches || script.matches.length === 0) {
            this.errors.push(`Content script ${index} has no matches`);
          }
          if (!script.js || script.js.length === 0) {
            this.errors.push(`Content script ${index} has no JS files`);
          }
        });
      }
      
      this.info.push(`✅ Manifest version: ${manifest.manifest_version}`);
      this.info.push(`✅ Extension name: ${manifest.name}`);
      this.info.push(`✅ Version: ${manifest.version}`);
      
    } catch (error) {
      this.errors.push(`Invalid manifest.json: ${error.message}`);
    }
  }
  
  validateFiles() {
    console.log('📁 Validating required files...');
    
    const requiredFiles = [
      'manifest.json',
      'background.js',
      'content-script.js',
      'popup.html',
      'popup.js',
      'popup.css',
      'content-styles.css'
    ];
    
    requiredFiles.forEach(file => {
      if (fs.existsSync(file)) {
        this.info.push(`✅ Found ${file}`);
      } else {
        this.errors.push(`Missing required file: ${file}`);
      }
    });
    
    // Check file sizes
    const maxSizes = {
      'background.js': 100000, // 100KB
      'content-script.js': 200000, // 200KB
      'popup.js': 100000, // 100KB
    };
    
    Object.entries(maxSizes).forEach(([file, maxSize]) => {
      if (fs.existsSync(file)) {
        const stats = fs.statSync(file);
        if (stats.size > maxSize) {
          this.warnings.push(`${file} is large (${Math.round(stats.size/1000)}KB) - consider optimization`);
        }
      }
    });
  }
  
  validatePermissions() {
    console.log('🔐 Validating permissions...');
    
    try {
      const manifest = JSON.parse(fs.readFileSync('manifest.json', 'utf8'));
      
      const permissions = manifest.permissions || [];
      const hostPermissions = manifest.host_permissions || [];
      
      // Check for overly broad permissions
      const broadPermissions = ['<all_urls>', 'http://*/*', 'https://*/*'];
      broadPermissions.forEach(perm => {
        if (hostPermissions.includes(perm)) {
          this.warnings.push(`Broad permission detected: ${perm} - consider limiting to specific domains`);
        }
      });
      
      // Check for sensitive permissions
      const sensitivePermissions = ['tabs', 'history', 'bookmarks', 'cookies'];
      sensitivePermissions.forEach(perm => {
        if (permissions.includes(perm)) {
          this.warnings.push(`Sensitive permission: ${perm} - ensure it's necessary`);
        }
      });
      
      this.info.push(`✅ Permissions: ${permissions.join(', ')}`);
      this.info.push(`✅ Host permissions: ${hostPermissions.length} domains`);
      
    } catch (error) {
      this.errors.push(`Could not validate permissions: ${error.message}`);
    }
  }
  
  validateIcons() {
    console.log('🎨 Validating icons...');
    
    const iconSizes = [16, 32, 48, 128];
    const iconDir = 'icons';
    
    if (!fs.existsSync(iconDir)) {
      this.errors.push('Icons directory not found');
      return;
    }
    
    iconSizes.forEach(size => {
      const pngIcon = path.join(iconDir, `icon${size}.png`);
      const svgIcon = path.join(iconDir, `icon${size}.svg`);
      
      if (fs.existsSync(pngIcon)) {
        this.info.push(`✅ Found icon${size}.png`);
      } else if (fs.existsSync(svgIcon)) {
        this.info.push(`✅ Found icon${size}.svg`);
        this.warnings.push(`Consider converting icon${size}.svg to PNG for better compatibility`);
      } else {
        this.warnings.push(`Missing icon for size ${size}x${size}`);
      }
    });
  }
  
  validateScripts() {
    console.log('📜 Validating JavaScript files...');
    
    const jsFiles = ['background.js', 'content-script.js', 'popup.js'];
    
    jsFiles.forEach(file => {
      if (fs.existsSync(file)) {
        try {
          const content = fs.readFileSync(file, 'utf8');
          
          // Check for common issues
          if (content.includes('eval(')) {
            this.warnings.push(`${file} contains eval() - not allowed in extensions`);
          }
          
          if (content.includes('innerHTML') && content.includes('user')) {
            this.warnings.push(`${file} may have XSS vulnerability with innerHTML`);
          }
          
          if (content.includes('http://')) {
            this.warnings.push(`${file} contains HTTP URLs - use HTTPS for security`);
          }
          
          // Check for required functions/classes
          if (file === 'background.js' && !content.includes('chrome.runtime.onInstalled')) {
            this.warnings.push('background.js missing onInstalled handler');
          }
          
          if (file === 'content-script.js' && !content.includes('chrome.runtime.onMessage')) {
            this.warnings.push('content-script.js missing message handler');
          }
          
          this.info.push(`✅ ${file} syntax appears valid`);
          
        } catch (error) {
          this.errors.push(`Error reading ${file}: ${error.message}`);
        }
      }
    });
  }
  
  printResults() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 VALIDATION RESULTS');
    console.log('='.repeat(50));
    
    if (this.info.length > 0) {
      console.log('\n✅ INFO:');
      this.info.forEach(msg => console.log(`   ${msg}`));
    }
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.warnings.forEach(msg => console.log(`   ${msg}`));
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      this.errors.forEach(msg => console.log(`   ${msg}`));
    }
    
    console.log('\n' + '='.repeat(50));
    
    if (this.errors.length === 0) {
      console.log('🎉 Extension validation PASSED!');
      console.log('✅ Ready for testing and deployment');
    } else {
      console.log('❌ Extension validation FAILED!');
      console.log(`   ${this.errors.length} error(s) need to be fixed`);
    }
    
    console.log(`   ${this.warnings.length} warning(s)`);
    console.log(`   ${this.info.length} info item(s)`);
    console.log('='.repeat(50));
  }
}

// Run validation
const validator = new ExtensionValidator();
const isValid = validator.validate();

process.exit(isValid ? 0 : 1);
