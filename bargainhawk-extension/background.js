// BargainHawk Chrome Extension - Background Script (Service Worker)
// Simplified version for Manifest V3 compatibility

console.log('BargainHawk background script loaded');

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  console.log('BargainHawk extension installed/updated:', details.reason);

  if (details.reason === 'install') {
    // Set default settings
    chrome.storage.local.set({
      'notifications': true,
      'autoDetect': true,
      'retailers': {
        'costco': true,
        'walmart': true,
        'wayfair': true,
        'bestbuy': true
      }
    });

    console.log('Default settings configured');
  }
});

// Handle messages from content scripts and popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Background received message:', message);

  switch (message.action) {
    case 'productDetected':
      handleProductDetected(message.data, sender);
      break;
    case 'showNotification':
      showNotification(message.title, message.message);
      break;
    case 'openTab':
      chrome.tabs.create({ url: message.url });
      break;
    default:
      console.log('Unknown message action:', message.action);
  }

  // Return true to indicate we'll send a response asynchronously
  return true;
});
  
// Helper functions
function handleProductDetected(productData, sender) {
  console.log('Product detected:', productData);

  // Store detected product data
  chrome.storage.local.set({
    'lastDetectedProduct': {
      ...productData,
      tabId: sender.tab.id,
      timestamp: Date.now()
    }
  });
}

function showNotification(title, message) {
  // Check if notifications are enabled
  chrome.storage.local.get(['notifications'], (result) => {
    if (result.notifications !== false) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: title,
        message: message
      });
    }
  });
}

// Handle tab updates to detect supported retailers
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    const supportedDomains = [
      'costco.ca', 'walmart.ca', 'wayfair.ca', 'bestbuy.ca',
      'canadiantire.ca', 'amazon.ca'
    ];

    const isSupported = supportedDomains.some(domain => tab.url.includes(domain));

    if (isSupported) {
      console.log('Supported retailer detected:', tab.url);

      // Inject content script if not already injected
      chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['content-script.js']
      }).catch(err => {
        // Script might already be injected, ignore error
        console.log('Content script injection skipped:', err.message);
      });
    }
  }
});

console.log('BargainHawk background script ready');
