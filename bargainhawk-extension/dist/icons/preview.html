<!DOCTYPE html>
<html>
<head>
  <title>BargainHawk Extension Icons Preview</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .icon-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; }
    .icon-item { background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .icon-item img { margin-bottom: 10px; }
    .dark-bg { background: #333; }
    .dark-bg img { background: #333; padding: 10px; border-radius: 4px; }
  </style>
</head>
<body>
  <h1>BargainHawk Extension Icons</h1>
  <div class="icon-grid">
    
      <div class="icon-item">
        <img src="icon16.svg" alt="16x16" width="16" height="16">
        <div>16x16px</div>
      </div>
    
      <div class="icon-item">
        <img src="icon32.svg" alt="32x32" width="32" height="32">
        <div>32x32px</div>
      </div>
    
      <div class="icon-item">
        <img src="icon48.svg" alt="48x48" width="48" height="48">
        <div>48x48px</div>
      </div>
    
      <div class="icon-item">
        <img src="icon128.svg" alt="128x128" width="128" height="128">
        <div>128x128px</div>
      </div>
    
  </div>
  
  <h2>Dark Background Preview</h2>
  <div class="icon-grid">
    
      <div class="icon-item dark-bg">
        <img src="icon16.svg" alt="16x16" width="16" height="16">
        <div style="color: white;">16x16px</div>
      </div>
    
      <div class="icon-item dark-bg">
        <img src="icon32.svg" alt="32x32" width="32" height="32">
        <div style="color: white;">32x32px</div>
      </div>
    
      <div class="icon-item dark-bg">
        <img src="icon48.svg" alt="48x48" width="48" height="48">
        <div style="color: white;">48x48px</div>
      </div>
    
      <div class="icon-item dark-bg">
        <img src="icon128.svg" alt="128x128" width="128" height="128">
        <div style="color: white;">128x128px</div>
      </div>
    
  </div>
</body>
</html>