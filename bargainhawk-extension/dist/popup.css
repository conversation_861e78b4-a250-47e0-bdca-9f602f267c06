/* BargainHawk Chrome Extension - Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 380px;
  min-height: 500px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f8fafc;
  color: #1e293b;
}

.screen {
  padding: 20px;
  min-height: 500px;
}

.hidden {
  display: none !important;
}

/* Loading Screen */
#loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.logo {
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
}

.logo-small {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.header h1 {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.header p {
  color: #64748b;
  font-size: 14px;
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
}

/* Forms */
.login-form {
  max-width: 300px;
  margin: 0 auto;
}

.login-form h2 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
  color: #374151;
}

.form-group input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  min-height: 40px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #4b5563;
}

.btn-outline {
  background: transparent;
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

.btn-outline:hover:not(:disabled) {
  background: #3b82f6;
  color: white;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
}

.btn-link {
  background: none;
  border: none;
  color: #3b82f6;
  text-decoration: underline;
  font-size: 12px;
  cursor: pointer;
}

.btn-back {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  margin-right: 8px;
}

/* Login Links */
.login-links {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  font-size: 12px;
}

.login-links a {
  color: #3b82f6;
  text-decoration: none;
}

.login-links a:hover {
  text-decoration: underline;
}

/* Retailer Section */
.retailer-section {
  margin-bottom: 20px;
}

.retailer-detected {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
}

.retailer-icon {
  font-size: 24px;
  margin-right: 12px;
}

.retailer-detected strong {
  display: block;
  font-size: 14px;
  margin-bottom: 2px;
}

.retailer-detected p {
  font-size: 12px;
  color: #64748b;
  margin: 0;
}

/* Product Section */
.product-section {
  margin-bottom: 20px;
}

.product-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.product-card {
  display: flex;
  padding: 12px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 16px;
}

.product-img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 12px;
}

.product-details h4 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  line-height: 1.3;
}

.product-price {
  font-size: 16px;
  font-weight: 700;
  color: #059669;
  margin-bottom: 2px;
}

.product-retailer {
  font-size: 12px;
  color: #64748b;
  margin: 0;
}

.price-target {
  margin-bottom: 16px;
}

.price-target label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
}

.price-input {
  display: flex;
  align-items: center;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  overflow: hidden;
}

.price-input span {
  padding: 10px 12px;
  background: #f9fafb;
  border-right: 1px solid #d1d5db;
  font-weight: 500;
}

.price-input input {
  flex: 1;
  padding: 10px 12px;
  border: none;
  font-size: 14px;
}

.price-input input:focus {
  outline: none;
}

/* Selection Section */
.selection-section {
  margin-bottom: 20px;
}

.selection-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.selection-section p {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 16px;
}

.selection-actions {
  display: flex;
  gap: 8px;
}

.selection-actions .btn {
  flex: 1;
}

/* Messages */
.message-area {
  margin-bottom: 16px;
}

.message {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 8px;
}

.message-icon {
  margin-right: 8px;
}

.message.success {
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.message.error {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  gap: 8px;
  margin-top: 20px;
}

.quick-actions .btn {
  flex: 1;
}

/* Settings */
.settings-content {
  max-height: 400px;
  overflow-y: auto;
}

.setting-group {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.setting-group:last-child {
  border-bottom: none;
}

.setting-group h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.retailer-toggles {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Toggle Switch */
.toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
}

.toggle input {
  display: none;
}

.toggle-slider {
  width: 44px;
  height: 24px;
  background: #cbd5e1;
  border-radius: 12px;
  position: relative;
  margin-right: 12px;
  transition: background 0.2s;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: transform 0.2s;
}

.toggle input:checked + .toggle-slider {
  background: #3b82f6;
}

.toggle input:checked + .toggle-slider::before {
  transform: translateX(20px);
}
