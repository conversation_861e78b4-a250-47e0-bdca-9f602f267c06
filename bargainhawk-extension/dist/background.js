// BargainHawk Chrome Extension - Background Script (Service Worker)
// Ultra-simplified version for maximum compatibility

console.log('BargainHawk background script starting...');

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  console.log('Extension installed:', details.reason);
  
  if (details.reason === 'install') {
    // Set basic settings
    chrome.storage.local.set({
      'extensionEnabled': true,
      'version': '1.0.0'
    }, () => {
      console.log('Settings initialized');
    });
  }
});

// Handle messages from content scripts and popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Message received:', message.action);
  
  try {
    switch (message.action) {
      case 'productDetected':
        console.log('Product detected:', message.data);
        sendResponse({ success: true });
        break;
        
      case 'ping':
        sendResponse({ success: true, message: 'Background script is running' });
        break;
        
      default:
        console.log('Unknown action:', message.action);
        sendResponse({ success: false, error: 'Unknown action' });
    }
  } catch (error) {
    console.error('Error handling message:', error);
    sendResponse({ success: false, error: error.message });
  }
  
  return true; // Keep message channel open
});

console.log('BargainHawk background script ready');
