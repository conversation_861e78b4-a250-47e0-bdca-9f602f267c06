// BargainHawk Chrome Extension - Debug Popup
console.log('Debug popup script loaded');

// Simple initialization that bypasses complex logic
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded - debug mode');
  
  // Hide loading screen immediately
  const loadingScreen = document.getElementById('loading');
  if (loadingScreen) {
    loadingScreen.classList.add('hidden');
    console.log('Loading screen hidden');
  }
  
  // Show login screen by default
  const loginScreen = document.getElementById('login-screen');
  if (loginScreen) {
    loginScreen.classList.remove('hidden');
    console.log('Login screen shown');
  }
  
  // Add basic form handler
  const loginForm = document.getElementById('login-form');
  if (loginForm) {
    loginForm.addEventListener('submit', (e) => {
      e.preventDefault();
      console.log('Login form submitted');
      
      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
      
      console.log('Email:', email);
      console.log('Password length:', password.length);
      
      // Show a simple message
      alert('Debug mode: Login form submitted. Check console for details.');
    });
    console.log('Login form handler added');
  }
  
  console.log('Debug popup initialization complete');
});

// Test Chrome APIs
setTimeout(() => {
  console.log('Testing Chrome APIs...');
  
  // Test storage
  chrome.storage.local.set({ test: 'value' }, () => {
    console.log('Storage test: SET successful');
    
    chrome.storage.local.get(['test'], (result) => {
      console.log('Storage test: GET result:', result);
    });
  });
  
  // Test tabs
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    console.log('Tabs test: Current tab:', tabs[0]?.url);
  });
  
}, 1000);
