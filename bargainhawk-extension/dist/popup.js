// BargainHawk Chrome Extension - Popup Logic
class BargainHawkPopup {
  constructor() {
    this.currentUser = null;
    this.currentTab = null;
    this.productData = null;
    // Configure API endpoint - UPDATE THIS FOR YOUR SETUP
    this.apiBaseUrl = 'http://localhost:8080'; // Local development
    // this.apiBaseUrl = 'https://your-scraper-app.fly.dev'; // Production

    this.init();
  }

  async init() {
    // Get current tab
    this.currentTab = await this.getCurrentTab();

    // Check authentication status
    await this.checkAuthStatus();

    // Setup event listeners
    this.setupEventListeners();

    // Initialize UI based on auth status
    this.initializeUI();
  }

  async getCurrentTab() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    return tab;
  }

  async checkAuthStatus() {
    try {
      const result = await chrome.storage.local.get(['supabaseToken', 'user']);
      if (result.supabaseToken && result.user) {
        this.currentUser = result.user;
        return true;
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    }
    return false;
  }

  setupEventListeners() {
    // Login form
    document.getElementById('login-form').addEventListener('submit', this.handleLogin.bind(this));

    // Logout
    document.getElementById('logout-btn').addEventListener('click', this.handleLogout.bind(this));

    // Auto-detect product
    document.getElementById('auto-detect-btn').addEventListener('click', this.handleAutoDetect.bind(this));

    // Manual price selection
    document.getElementById('manual-select-btn').addEventListener('click', this.handleManualSelect.bind(this));

    // Add product
    document.getElementById('add-product-btn').addEventListener('click', this.handleAddProduct.bind(this));

    // Settings
    document.getElementById('settings-btn').addEventListener('click', this.showSettings.bind(this));
    document.getElementById('settings-back-btn').addEventListener('click', this.hideSettings.bind(this));

    // Save settings
    document.getElementById('save-settings-btn').addEventListener('click', this.saveSettings.bind(this));
  }

  async initializeUI() {
    const isAuthenticated = await this.checkAuthStatus();

    if (isAuthenticated) {
      await this.showMainScreen();
    } else {
      this.showLoginScreen();
    }
  }

  showLoginScreen() {
    document.getElementById('login-screen').style.display = 'block';
    document.getElementById('main-screen').style.display = 'none';
    document.getElementById('product-preview').style.display = 'none';
    document.getElementById('settings-screen').style.display = 'none';
  }

  async showMainScreen() {
    document.getElementById('login-screen').style.display = 'none';
    document.getElementById('main-screen').style.display = 'block';
    document.getElementById('product-preview').style.display = 'none';
    document.getElementById('settings-screen').style.display = 'none';

    // Update user info
    if (this.currentUser) {
      document.getElementById('user-email').textContent = this.currentUser.email || 'User';
    }

    // Check if current site is supported
    await this.checkCurrentSite();
  }

  async checkCurrentSite() {
    if (!this.currentTab) return;

    const url = this.currentTab.url;
    const supportedSites = [
      'costco.ca', 'walmart.ca', 'wayfair.ca', 'bestbuy.ca',
      'canadiantire.ca', 'amazon.ca'
    ];

    const isSupported = supportedSites.some(site => url.includes(site));

    const statusEl = document.getElementById('site-status');
    const actionsEl = document.getElementById('product-actions');

    if (isSupported) {
      statusEl.innerHTML = '<span class="status-supported">✅ Supported retailer detected</span>';
      actionsEl.style.display = 'block';
    } else {
      statusEl.innerHTML = '<span class="status-unsupported">❌ This retailer is not supported yet</span>';
      actionsEl.style.display = 'none';
    }
  }

  async handleLogin(event) {
    event.preventDefault();

    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const loginBtn = document.getElementById('login-btn');

    if (!email || !password) {
      this.showError('Please enter both email and password');
      return;
    }

    loginBtn.textContent = 'Signing in...';
    loginBtn.disabled = true;

    try {
      // Use Supabase authentication (you'll need to implement this endpoint in your backend)
      const response = await fetch(`${this.apiBaseUrl}/auth/supabase-login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
      });

      const data = await response.json();

      if (response.ok && data.access_token) {
        // Save Supabase token
        await chrome.storage.local.set({
          supabaseToken: data.access_token,
          user: data.user
        });

        this.currentUser = data.user;
        await this.showMainScreen();
      } else {
        this.showError(data.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      this.showError('Network error. Please check your connection and try again.');
    } finally {
      loginBtn.textContent = 'Sign In';
      loginBtn.disabled = false;
    }
  }

  async handleLogout() {
    try {
      await chrome.storage.local.clear();
      this.currentUser = null;
      this.productData = null;
      this.showLoginScreen();
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  async handleAutoDetect() {
    const detectBtn = document.getElementById('auto-detect-btn');
    detectBtn.textContent = 'Detecting...';
    detectBtn.disabled = true;

    try {
      // Send message to content script to detect product
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'detectProduct'
      });

      if (response && response.success) {
        this.productData = response.data;
        this.showProductPreview();
      } else {
        this.showError(response?.error || 'Could not detect product. Try manual selection.');
      }
    } catch (error) {
      console.error('Auto-detect error:', error);
      this.showError('Failed to detect product. Please try manual selection.');
    } finally {
      detectBtn.textContent = 'Auto-Detect Product';
      detectBtn.disabled = false;
    }
  }

  async handleManualSelect() {
    try {
      // Send message to content script to start manual selection
      await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'startManualSelection'
      });

      // Close popup to let user interact with page
      window.close();
    } catch (error) {
      console.error('Manual select error:', error);
      this.showError('Failed to start manual selection.');
    }
  }

  showProductPreview() {
    if (!this.productData) return;

    document.getElementById('main-screen').style.display = 'none';
    document.getElementById('product-preview').style.display = 'block';

    // Populate product data
    document.getElementById('preview-image').src = this.productData.image || '/icons/icon48.png';
    document.getElementById('preview-name').textContent = this.productData.name || 'Unknown Product';
    document.getElementById('preview-price').textContent = this.productData.price ? `$${this.productData.price}` : 'Price not found';
    document.getElementById('preview-url').textContent = this.productData.url || this.currentTab.url;

    // Set default target price (10% below current price)
    if (this.productData.price) {
      const suggestedTarget = Math.round(this.productData.price * 0.9 * 100) / 100;
      document.getElementById('target-price').value = suggestedTarget;
    }
  }

  async handleAddProduct() {
    if (!this.productData) {
      this.showError('No product data available');
      return;
    }

    const addBtn = document.getElementById('add-product-btn');
    const targetPrice = parseFloat(document.getElementById('target-price').value);

    addBtn.textContent = 'Adding...';
    addBtn.disabled = true;

    try {
      const result = await chrome.storage.local.get(['supabaseToken']);
      if (!result.supabaseToken) {
        throw new Error('Not authenticated');
      }

      // Prepare product data for your API
      const productPayload = {
        url: this.productData.url || this.currentTab.url,
        targetPrice: targetPrice || null,
        province: 'ON', // Default to Ontario, could be made configurable
        // Add other fields as needed by your API
      };

      const response = await fetch(`${this.apiBaseUrl}/fetch-url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${result.supabaseToken}`
        },
        body: JSON.stringify(productPayload)
      });

      if (response.ok) {
        this.showSuccess('Product added successfully! 🎉');
        // Close popup after success
        setTimeout(() => window.close(), 2000);
      } else {
        const errorData = await response.text();
        if (errorData.includes('Cannot exceed 5 products')) {
          throw new Error('You\'ve reached the 5-product limit. Remove a product to add a new one.');
        }
        throw new Error(errorData || 'Failed to add product');
      }
    } catch (error) {
      console.error('Add product error:', error);
      this.showError(error.message || 'Failed to add product');
    } finally {
      addBtn.textContent = 'Add to BargainHawk';
      addBtn.disabled = false;
    }
  }

  showSettings() {
    document.getElementById('main-screen').style.display = 'none';
    document.getElementById('settings-screen').style.display = 'block';

    // Load current settings
    this.loadSettings();
  }

  hideSettings() {
    document.getElementById('settings-screen').style.display = 'none';
    document.getElementById('main-screen').style.display = 'block';
  }

  async loadSettings() {
    try {
      const settings = await chrome.storage.local.get([
        'notifications',
        'autoDetect',
        'retailers'
      ]);

      document.getElementById('notifications-toggle').checked = settings.notifications !== false;
      document.getElementById('auto-detect-toggle').checked = settings.autoDetect !== false;

      // Load retailer toggles
      const retailers = settings.retailers || {};
      document.getElementById('costco-toggle').checked = retailers.costco !== false;
      document.getElementById('walmart-toggle').checked = retailers.walmart !== false;
      document.getElementById('wayfair-toggle').checked = retailers.wayfair !== false;
      document.getElementById('bestbuy-toggle').checked = retailers.bestbuy !== false;
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }

  async saveSettings() {
    try {
      const settings = {
        notifications: document.getElementById('notifications-toggle').checked,
        autoDetect: document.getElementById('auto-detect-toggle').checked,
        retailers: {
          costco: document.getElementById('costco-toggle').checked,
          walmart: document.getElementById('walmart-toggle').checked,
          wayfair: document.getElementById('wayfair-toggle').checked,
          bestbuy: document.getElementById('bestbuy-toggle').checked,
        }
      };

      await chrome.storage.local.set(settings);
      this.showSuccess('Settings saved!');
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showError('Failed to save settings');
    }
  }

  showError(message) {
    const errorEl = document.getElementById('error-message');
    errorEl.textContent = message;
    errorEl.style.display = 'block';

    // Hide after 5 seconds
    setTimeout(() => {
      errorEl.style.display = 'none';
    }, 5000);
  }

  showSuccess(message) {
    const successEl = document.getElementById('success-message');
    successEl.textContent = message;
    successEl.style.display = 'block';

    // Hide after 3 seconds
    setTimeout(() => {
      successEl.style.display = 'none';
    }, 3000);
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new BargainHawkPopup();
});